import { useNavigate, useLocation } from 'react-router-dom'
import React from 'react'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import StatsCard from '../../components/StatsCard'

export default function AdminDashboard() {
  const navigate = useNavigate()
  const location = useLocation()

  // show the three buttons only when at the admin root (not on nested /admin/* pages)
  const atAdminRoot = location.pathname.replace(/\/$/, '') === '/admin'

  // Mock data for dashboard stats
  const stats = [
    {
      title: 'Total Passengers',
      value: '1,248',
      subtitle: 'Active bookings',
      icon: '👥',
      trend: '+12% from last month',
      trendDirection: 'up',
      color: 'blue',
      onClick: () => navigate('passengers')
    },
    {
      title: 'Active Flights',
      value: '156',
      subtitle: 'Scheduled today',
      icon: '✈️',
      trend: '+5% from yesterday',
      trendDirection: 'up',
      color: 'green',
      onClick: () => navigate('flights')
    },
    {
      title: 'Routes Available',
      value: '48',
      subtitle: 'Domestic & International',
      icon: '🗺️',
      trend: '2 new routes',
      trendDirection: 'up',
      color: 'purple',
      onClick: () => navigate('routes')
    },
    {
      title: 'Revenue Today',
      value: '$125K',
      subtitle: 'From bookings',
      icon: '💰',
      trend: '+8% from yesterday',
      trendDirection: 'up',
      color: 'yellow'
    }
  ];

  const quickActions = [
    {
      title: 'Manage Passengers',
      description: 'View and manage passenger information, bookings, and travel history',
      icon: '👥',
      color: 'blue',
      onClick: () => navigate('passengers')
    },
    {
      title: 'Manage Flights',
      description: 'Schedule flights, update statuses, and manage aircraft assignments',
      icon: '✈️',
      color: 'green',
      onClick: () => navigate('flights')
    },
    {
      title: 'Manage Routes',
      description: 'Create and modify flight routes, airports, and destinations',
      icon: '🗺️',
      color: 'purple',
      onClick: () => navigate('routes')
    },
    {
      title: 'Travel History',
      description: 'View passenger travel records and analytics',
      icon: '📊',
      color: 'indigo',
      onClick: () => navigate('travel-history')
    },
    {
      title: 'User Management',
      description: 'Manage staff accounts, roles, and permissions',
      icon: '⚙️',
      color: 'red',
      onClick: () => navigate('users')
    }
  ];

  return (
    <div className="space-y-6">
      {atAdminRoot ? (
        <>
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <StatsCard
                key={index}
                title={stat.title}
                value={stat.value}
                subtitle={stat.subtitle}
                icon={stat.icon}
                trend={stat.trend}
                trendDirection={stat.trendDirection}
                color={stat.color}
                onClick={stat.onClick}
              />
            ))}
          </div>

          {/* Quick Actions */}
          <Card>
            <Card.Header>
              <Card.Title>Quick Actions</Card.Title>
            </Card.Header>
            <Card.Content>
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                {quickActions.map((action, index) => (
                  <Card 
                    key={index}
                    className="cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105"
                    onClick={action.onClick}
                  >
                    <div className="flex items-start space-x-4">
                      <div className={`w-12 h-12 rounded-lg bg-${action.color}-500 flex items-center justify-center text-white text-xl flex-shrink-0`}>
                        {action.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-gray-900 mb-1">{action.title}</h3>
                        <p className="text-sm text-gray-600 line-clamp-2">{action.description}</p>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </Card.Content>
          </Card>

          {/* Recent Activity */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <Card>
              <Card.Header>
                <Card.Title>Recent Bookings</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="space-y-4">
                  {[
                    { passenger: 'John Doe', flight: 'AA123', destination: 'New York', time: '2 mins ago' },
                    { passenger: 'Sarah Wilson', flight: 'BA456', destination: 'London', time: '15 mins ago' },
                    { passenger: 'Mike Johnson', flight: 'UA789', destination: 'Tokyo', time: '1 hour ago' }
                  ].map((booking, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{booking.passenger}</p>
                        <p className="text-sm text-gray-600">{booking.flight} to {booking.destination}</p>
                      </div>
                      <span className="text-xs text-gray-500">{booking.time}</span>
                    </div>
                  ))}
                </div>
              </Card.Content>
            </Card>

            <Card>
              <Card.Header>
                <Card.Title>System Alerts</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="space-y-4">
                  {[
                    { type: 'warning', message: 'Flight AA123 delayed by 30 minutes', time: '5 mins ago' },
                    { type: 'info', message: 'New passenger registered: Jane Smith', time: '10 mins ago' },
                    { type: 'success', message: 'Flight BA456 departed on time', time: '25 mins ago' }
                  ].map((alert, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        alert.type === 'warning' ? 'bg-yellow-500' :
                        alert.type === 'info' ? 'bg-blue-500' : 'bg-green-500'
                      }`} />
                      <div className="flex-1">
                        <p className="text-sm text-gray-900">{alert.message}</p>
                        <span className="text-xs text-gray-500">{alert.time}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </Card.Content>
            </Card>
          </div>
        </>
      ) : (
        <div className="mb-4">
          <Button variant="outline" onClick={() => navigate('/admin')}>
            ← Back to Dashboard
          </Button>
        </div>
      )}
    </div>
  )
}
