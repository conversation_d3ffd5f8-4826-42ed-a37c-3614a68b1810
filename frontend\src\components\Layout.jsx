// Main layout wrapper component
import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import Button from './ui/Button';

const Layout = ({ children, title, showNavigation = true }) => {
  const location = useLocation();
  const navigate = useNavigate();

  const navigationItems = [
    { path: '/admin', label: 'Admin', icon: '👤' },
    { path: '/staff', label: 'Staff', icon: '👥' },
    { path: '/passenger', label: 'Passenger', icon: '✈️' }
  ];

  const handleLogout = () => {
    // Clear any auth data
    localStorage.removeItem('authToken');
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link to="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">✈</span>
                </div>
                <span className="font-bold text-xl text-gray-900">AirlineMS</span>
              </Link>
              {title && (
                <div className="hidden md:block">
                  <span className="text-gray-400">|</span>
                  <span className="ml-4 text-lg font-medium text-gray-700">{title}</span>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-4">
              {showNavigation && (
                <nav className="hidden md:flex space-x-1">
                  {navigationItems.map((item) => (
                    <Link
                      key={item.path}
                      to={item.path}
                      className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                        location.pathname.startsWith(item.path)
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                      }`}
                    >
                      <span className="mr-2">{item.icon}</span>
                      {item.label}
                    </Link>
                  ))}
                </nav>
              )}
              
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 text-sm font-medium">U</span>
                </div>
                <Button variant="ghost" size="sm" onClick={handleLogout}>
                  Logout
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {children}
      </main>
    </div>
  );
};

export default Layout;
