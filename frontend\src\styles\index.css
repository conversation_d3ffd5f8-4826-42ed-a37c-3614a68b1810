/* Import Inter font - must come first */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@import "tailwindcss";

/* Base styles */
@layer base {
  html {
    @apply antialiased;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
  
  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
  
  /* Focus styles */
  *:focus {
    outline: none;
  }
  
  /* Selection styles */
  ::selection {
    @apply bg-blue-600 text-white;
  }
}

/* Component styles */
@layer components {
  /* Container styles */
  .container-flight {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .container-admin {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  /* Card styles */
  .card-base {
    @apply bg-white rounded-xl shadow-md;
  }
  
  .card-hover {
    @apply hover:shadow-lg transition-shadow duration-200;
  }
  
  /* Input styles */
  .input-base {
    @apply w-full px-4 py-2.5 border border-gray-300 rounded-lg text-sm
           focus:ring-2 focus:ring-blue-500 focus:border-transparent
           placeholder-gray-400 transition-all duration-200;
  }
  
  /* Button styles */
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium
           px-6 py-2.5 rounded-lg shadow-md hover:shadow-lg
           transition-all duration-200 transform hover:-translate-y-0.5;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium
           px-6 py-2.5 rounded-lg transition-all duration-200;
  }
  
  /* Badge styles */
  .badge-pending {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .badge-completed {
    @apply bg-green-100 text-green-800;
  }
  
  .badge-refunded {
    @apply bg-red-100 text-red-800;
  }
  
  /* Sidebar styles */
  .sidebar-item {
    @apply flex items-center gap-3 px-4 py-3 rounded-lg
           text-gray-700 hover:bg-gray-100
           transition-all duration-200;
  }
  
  .sidebar-item-active {
    @apply bg-indigo-600 text-white hover:bg-indigo-700;
  }
  
  /* Table styles */
  .table-header {
    @apply bg-gray-50 border-b border-gray-200;
  }
  
  .table-row {
    @apply border-b border-gray-100 hover:bg-gray-50
           transition-colors duration-150;
  }
  
  /* Flight card styles */
  .flight-card {
    @apply bg-white rounded-xl p-6 shadow-md hover:shadow-lg
           transition-all duration-200 border border-gray-100;
  }
  
  /* Seat styles */
  .seat-available {
    @apply bg-white border-2 border-gray-300 hover:border-blue-500
           hover:bg-blue-50 cursor-pointer transition-all duration-200;
  }
  
  .seat-selected {
    @apply bg-blue-600 border-2 border-blue-600 text-white;
  }
  
  .seat-unavailable {
    @apply bg-gray-100 border-2 border-gray-200 text-gray-400
           cursor-not-allowed;
  }
  
  /* Progress indicator */
  .progress-dot {
    @apply w-3 h-3 rounded-full bg-gray-300;
  }
  
  .progress-dot-active {
    @apply bg-blue-600;
  }
  
  .progress-line {
    @apply h-0.5 bg-gray-300 flex-1;
  }
  
  .progress-line-active {
    @apply bg-blue-600;
  }
}

/* Utility styles */
@layer utilities {
  /* Text truncation */
  .truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* Gradient backgrounds */
  .bg-sky-gradient {
    background: linear-gradient(to bottom, #87CEEB 0%, #98D8E8 100%);
  }
  
  .bg-admin-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gray-400 rounded-full hover:bg-gray-500;
  }
  
  /* Loading animation */
  .loading-spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

/* Responsive utilities */
@media (max-width: 640px) {
  .container-flight,
  .container-admin {
    @apply px-3;
  }
  
  .card-base {
    @apply rounded-lg;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
