import { useLocation, Outlet } from 'react-router-dom'
import Layout from '../components/Layout'
import AdminDashboard from '../features/admin/AdminDashboard'

export default function AdminHome() {
  const { state } = useLocation()
  const name = state?.name || 'Admin'
  
  return (
    <Layout title="Admin Dashboard" showNavigation={true}>
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white">
          <h1 className="text-3xl font-bold mb-2">Welcome back, {name}</h1>
          <p className="text-blue-100">Manage your airline operations from this dashboard</p>
        </div>
        
        <AdminDashboard />
        
        <div className="mt-6">
          <Outlet />
        </div>
      </div>
    </Layout>
  )
}
