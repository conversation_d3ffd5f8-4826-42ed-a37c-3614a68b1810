function Zg(s,f){for(var d=0;d<f.length;d++){const r=f[d];if(typeof r!="string"&&!Array.isArray(r)){for(const h in r)if(h!=="default"&&!(h in s)){const p=Object.getOwnPropertyDescriptor(r,h);p&&Object.defineProperty(s,h,p.get?p:{enumerable:!0,get:()=>r[h]})}}}return Object.freeze(Object.defineProperty(s,Symbol.toStringTag,{value:"Module"}))}(function(){const f=document.createElement("link").relList;if(f&&f.supports&&f.supports("modulepreload"))return;for(const h of document.querySelectorAll('link[rel="modulepreload"]'))r(h);new MutationObserver(h=>{for(const p of h)if(p.type==="childList")for(const S of p.addedNodes)S.tagName==="LINK"&&S.rel==="modulepreload"&&r(S)}).observe(document,{childList:!0,subtree:!0});function d(h){const p={};return h.integrity&&(p.integrity=h.integrity),h.referrerPolicy&&(p.referrerPolicy=h.referrerPolicy),h.crossOrigin==="use-credentials"?p.credentials="include":h.crossOrigin==="anonymous"?p.credentials="omit":p.credentials="same-origin",p}function r(h){if(h.ep)return;h.ep=!0;const p=d(h);fetch(h.href,p)}})();function Vg(s){return s&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s}var xc={exports:{}},Bn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cd;function Kg(){if(Cd)return Bn;Cd=1;var s=Symbol.for("react.transitional.element"),f=Symbol.for("react.fragment");function d(r,h,p){var S=null;if(p!==void 0&&(S=""+p),h.key!==void 0&&(S=""+h.key),"key"in h){p={};for(var _ in h)_!=="key"&&(p[_]=h[_])}else p=h;return h=p.ref,{$$typeof:s,type:r,key:S,ref:h!==void 0?h:null,props:p}}return Bn.Fragment=f,Bn.jsx=d,Bn.jsxs=d,Bn}var _d;function Jg(){return _d||(_d=1,xc.exports=Kg()),xc.exports}var i=Jg(),bc={exports:{}},ne={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ud;function kg(){if(Ud)return ne;Ud=1;var s=Symbol.for("react.transitional.element"),f=Symbol.for("react.portal"),d=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),h=Symbol.for("react.profiler"),p=Symbol.for("react.consumer"),S=Symbol.for("react.context"),_=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),T=Symbol.for("react.lazy"),y=Symbol.iterator;function N(x){return x===null||typeof x!="object"?null:(x=y&&x[y]||x["@@iterator"],typeof x=="function"?x:null)}var z={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},C=Object.assign,G={};function M(x,Y,J){this.props=x,this.context=Y,this.refs=G,this.updater=J||z}M.prototype.isReactComponent={},M.prototype.setState=function(x,Y){if(typeof x!="object"&&typeof x!="function"&&x!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,x,Y,"setState")},M.prototype.forceUpdate=function(x){this.updater.enqueueForceUpdate(this,x,"forceUpdate")};function L(){}L.prototype=M.prototype;function K(x,Y,J){this.props=x,this.context=Y,this.refs=G,this.updater=J||z}var Q=K.prototype=new L;Q.constructor=K,C(Q,M.prototype),Q.isPureReactComponent=!0;var W=Array.isArray,w={H:null,A:null,T:null,S:null,V:null},fe=Object.prototype.hasOwnProperty;function Ce(x,Y,J,V,P,oe){return J=oe.ref,{$$typeof:s,type:x,key:Y,ref:J!==void 0?J:null,props:oe}}function Fe(x,Y){return Ce(x.type,Y,void 0,void 0,void 0,x.props)}function ht(x){return typeof x=="object"&&x!==null&&x.$$typeof===s}function $t(x){var Y={"=":"=0",":":"=2"};return"$"+x.replace(/[=:]/g,function(J){return Y[J]})}var X=/\/+/g;function F(x,Y){return typeof x=="object"&&x!==null&&x.key!=null?$t(""+x.key):Y.toString(36)}function je(){}function we(x){switch(x.status){case"fulfilled":return x.value;case"rejected":throw x.reason;default:switch(typeof x.status=="string"?x.then(je,je):(x.status="pending",x.then(function(Y){x.status==="pending"&&(x.status="fulfilled",x.value=Y)},function(Y){x.status==="pending"&&(x.status="rejected",x.reason=Y)})),x.status){case"fulfilled":return x.value;case"rejected":throw x.reason}}throw x}function Ae(x,Y,J,V,P){var oe=typeof x;(oe==="undefined"||oe==="boolean")&&(x=null);var ae=!1;if(x===null)ae=!0;else switch(oe){case"bigint":case"string":case"number":ae=!0;break;case"object":switch(x.$$typeof){case s:case f:ae=!0;break;case T:return ae=x._init,Ae(ae(x._payload),Y,J,V,P)}}if(ae)return P=P(x),ae=V===""?"."+F(x,0):V,W(P)?(J="",ae!=null&&(J=ae.replace(X,"$&/")+"/"),Ae(P,Y,J,"",function(Ft){return Ft})):P!=null&&(ht(P)&&(P=Fe(P,J+(P.key==null||x&&x.key===P.key?"":(""+P.key).replace(X,"$&/")+"/")+ae)),Y.push(P)),1;ae=0;var at=V===""?".":V+":";if(W(x))for(var Te=0;Te<x.length;Te++)V=x[Te],oe=at+F(V,Te),ae+=Ae(V,Y,J,oe,P);else if(Te=N(x),typeof Te=="function")for(x=Te.call(x),Te=0;!(V=x.next()).done;)V=V.value,oe=at+F(V,Te++),ae+=Ae(V,Y,J,oe,P);else if(oe==="object"){if(typeof x.then=="function")return Ae(we(x),Y,J,V,P);throw Y=String(x),Error("Objects are not valid as a React child (found: "+(Y==="[object Object]"?"object with keys {"+Object.keys(x).join(", ")+"}":Y)+"). If you meant to render a collection of children, use an array instead.")}return ae}function U(x,Y,J){if(x==null)return x;var V=[],P=0;return Ae(x,V,"","",function(oe){return Y.call(J,oe,P++)}),V}function Z(x){if(x._status===-1){var Y=x._result;Y=Y(),Y.then(function(J){(x._status===0||x._status===-1)&&(x._status=1,x._result=J)},function(J){(x._status===0||x._status===-1)&&(x._status=2,x._result=J)}),x._status===-1&&(x._status=0,x._result=Y)}if(x._status===1)return x._result.default;throw x._result}var te=typeof reportError=="function"?reportError:function(x){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Y=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof x=="object"&&x!==null&&typeof x.message=="string"?String(x.message):String(x),error:x});if(!window.dispatchEvent(Y))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",x);return}console.error(x)};function xe(){}return ne.Children={map:U,forEach:function(x,Y,J){U(x,function(){Y.apply(this,arguments)},J)},count:function(x){var Y=0;return U(x,function(){Y++}),Y},toArray:function(x){return U(x,function(Y){return Y})||[]},only:function(x){if(!ht(x))throw Error("React.Children.only expected to receive a single React element child.");return x}},ne.Component=M,ne.Fragment=d,ne.Profiler=h,ne.PureComponent=K,ne.StrictMode=r,ne.Suspense=g,ne.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=w,ne.__COMPILER_RUNTIME={__proto__:null,c:function(x){return w.H.useMemoCache(x)}},ne.cache=function(x){return function(){return x.apply(null,arguments)}},ne.cloneElement=function(x,Y,J){if(x==null)throw Error("The argument must be a React element, but you passed "+x+".");var V=C({},x.props),P=x.key,oe=void 0;if(Y!=null)for(ae in Y.ref!==void 0&&(oe=void 0),Y.key!==void 0&&(P=""+Y.key),Y)!fe.call(Y,ae)||ae==="key"||ae==="__self"||ae==="__source"||ae==="ref"&&Y.ref===void 0||(V[ae]=Y[ae]);var ae=arguments.length-2;if(ae===1)V.children=J;else if(1<ae){for(var at=Array(ae),Te=0;Te<ae;Te++)at[Te]=arguments[Te+2];V.children=at}return Ce(x.type,P,void 0,void 0,oe,V)},ne.createContext=function(x){return x={$$typeof:S,_currentValue:x,_currentValue2:x,_threadCount:0,Provider:null,Consumer:null},x.Provider=x,x.Consumer={$$typeof:p,_context:x},x},ne.createElement=function(x,Y,J){var V,P={},oe=null;if(Y!=null)for(V in Y.key!==void 0&&(oe=""+Y.key),Y)fe.call(Y,V)&&V!=="key"&&V!=="__self"&&V!=="__source"&&(P[V]=Y[V]);var ae=arguments.length-2;if(ae===1)P.children=J;else if(1<ae){for(var at=Array(ae),Te=0;Te<ae;Te++)at[Te]=arguments[Te+2];P.children=at}if(x&&x.defaultProps)for(V in ae=x.defaultProps,ae)P[V]===void 0&&(P[V]=ae[V]);return Ce(x,oe,void 0,void 0,null,P)},ne.createRef=function(){return{current:null}},ne.forwardRef=function(x){return{$$typeof:_,render:x}},ne.isValidElement=ht,ne.lazy=function(x){return{$$typeof:T,_payload:{_status:-1,_result:x},_init:Z}},ne.memo=function(x,Y){return{$$typeof:m,type:x,compare:Y===void 0?null:Y}},ne.startTransition=function(x){var Y=w.T,J={};w.T=J;try{var V=x(),P=w.S;P!==null&&P(J,V),typeof V=="object"&&V!==null&&typeof V.then=="function"&&V.then(xe,te)}catch(oe){te(oe)}finally{w.T=Y}},ne.unstable_useCacheRefresh=function(){return w.H.useCacheRefresh()},ne.use=function(x){return w.H.use(x)},ne.useActionState=function(x,Y,J){return w.H.useActionState(x,Y,J)},ne.useCallback=function(x,Y){return w.H.useCallback(x,Y)},ne.useContext=function(x){return w.H.useContext(x)},ne.useDebugValue=function(){},ne.useDeferredValue=function(x,Y){return w.H.useDeferredValue(x,Y)},ne.useEffect=function(x,Y,J){var V=w.H;if(typeof J=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return V.useEffect(x,Y)},ne.useId=function(){return w.H.useId()},ne.useImperativeHandle=function(x,Y,J){return w.H.useImperativeHandle(x,Y,J)},ne.useInsertionEffect=function(x,Y){return w.H.useInsertionEffect(x,Y)},ne.useLayoutEffect=function(x,Y){return w.H.useLayoutEffect(x,Y)},ne.useMemo=function(x,Y){return w.H.useMemo(x,Y)},ne.useOptimistic=function(x,Y){return w.H.useOptimistic(x,Y)},ne.useReducer=function(x,Y,J){return w.H.useReducer(x,Y,J)},ne.useRef=function(x){return w.H.useRef(x)},ne.useState=function(x){return w.H.useState(x)},ne.useSyncExternalStore=function(x,Y,J){return w.H.useSyncExternalStore(x,Y,J)},ne.useTransition=function(){return w.H.useTransition()},ne.version="19.1.1",ne}var Bd;function zc(){return Bd||(Bd=1,bc.exports=kg()),bc.exports}var O=zc();const $g=Vg(O),Fg=Zg({__proto__:null,default:$g},[O]);var Sc={exports:{}},Hn={},jc={exports:{}},Nc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hd;function Wg(){return Hd||(Hd=1,(function(s){function f(U,Z){var te=U.length;U.push(Z);e:for(;0<te;){var xe=te-1>>>1,x=U[xe];if(0<h(x,Z))U[xe]=Z,U[te]=x,te=xe;else break e}}function d(U){return U.length===0?null:U[0]}function r(U){if(U.length===0)return null;var Z=U[0],te=U.pop();if(te!==Z){U[0]=te;e:for(var xe=0,x=U.length,Y=x>>>1;xe<Y;){var J=2*(xe+1)-1,V=U[J],P=J+1,oe=U[P];if(0>h(V,te))P<x&&0>h(oe,V)?(U[xe]=oe,U[P]=te,xe=P):(U[xe]=V,U[J]=te,xe=J);else if(P<x&&0>h(oe,te))U[xe]=oe,U[P]=te,xe=P;else break e}}return Z}function h(U,Z){var te=U.sortIndex-Z.sortIndex;return te!==0?te:U.id-Z.id}if(s.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var p=performance;s.unstable_now=function(){return p.now()}}else{var S=Date,_=S.now();s.unstable_now=function(){return S.now()-_}}var g=[],m=[],T=1,y=null,N=3,z=!1,C=!1,G=!1,M=!1,L=typeof setTimeout=="function"?setTimeout:null,K=typeof clearTimeout=="function"?clearTimeout:null,Q=typeof setImmediate<"u"?setImmediate:null;function W(U){for(var Z=d(m);Z!==null;){if(Z.callback===null)r(m);else if(Z.startTime<=U)r(m),Z.sortIndex=Z.expirationTime,f(g,Z);else break;Z=d(m)}}function w(U){if(G=!1,W(U),!C)if(d(g)!==null)C=!0,fe||(fe=!0,F());else{var Z=d(m);Z!==null&&Ae(w,Z.startTime-U)}}var fe=!1,Ce=-1,Fe=5,ht=-1;function $t(){return M?!0:!(s.unstable_now()-ht<Fe)}function X(){if(M=!1,fe){var U=s.unstable_now();ht=U;var Z=!0;try{e:{C=!1,G&&(G=!1,K(Ce),Ce=-1),z=!0;var te=N;try{t:{for(W(U),y=d(g);y!==null&&!(y.expirationTime>U&&$t());){var xe=y.callback;if(typeof xe=="function"){y.callback=null,N=y.priorityLevel;var x=xe(y.expirationTime<=U);if(U=s.unstable_now(),typeof x=="function"){y.callback=x,W(U),Z=!0;break t}y===d(g)&&r(g),W(U)}else r(g);y=d(g)}if(y!==null)Z=!0;else{var Y=d(m);Y!==null&&Ae(w,Y.startTime-U),Z=!1}}break e}finally{y=null,N=te,z=!1}Z=void 0}}finally{Z?F():fe=!1}}}var F;if(typeof Q=="function")F=function(){Q(X)};else if(typeof MessageChannel<"u"){var je=new MessageChannel,we=je.port2;je.port1.onmessage=X,F=function(){we.postMessage(null)}}else F=function(){L(X,0)};function Ae(U,Z){Ce=L(function(){U(s.unstable_now())},Z)}s.unstable_IdlePriority=5,s.unstable_ImmediatePriority=1,s.unstable_LowPriority=4,s.unstable_NormalPriority=3,s.unstable_Profiling=null,s.unstable_UserBlockingPriority=2,s.unstable_cancelCallback=function(U){U.callback=null},s.unstable_forceFrameRate=function(U){0>U||125<U?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Fe=0<U?Math.floor(1e3/U):5},s.unstable_getCurrentPriorityLevel=function(){return N},s.unstable_next=function(U){switch(N){case 1:case 2:case 3:var Z=3;break;default:Z=N}var te=N;N=Z;try{return U()}finally{N=te}},s.unstable_requestPaint=function(){M=!0},s.unstable_runWithPriority=function(U,Z){switch(U){case 1:case 2:case 3:case 4:case 5:break;default:U=3}var te=N;N=U;try{return Z()}finally{N=te}},s.unstable_scheduleCallback=function(U,Z,te){var xe=s.unstable_now();switch(typeof te=="object"&&te!==null?(te=te.delay,te=typeof te=="number"&&0<te?xe+te:xe):te=xe,U){case 1:var x=-1;break;case 2:x=250;break;case 5:x=1073741823;break;case 4:x=1e4;break;default:x=5e3}return x=te+x,U={id:T++,callback:Z,priorityLevel:U,startTime:te,expirationTime:x,sortIndex:-1},te>xe?(U.sortIndex=te,f(m,U),d(g)===null&&U===d(m)&&(G?(K(Ce),Ce=-1):G=!0,Ae(w,te-xe))):(U.sortIndex=x,f(g,U),C||z||(C=!0,fe||(fe=!0,F()))),U},s.unstable_shouldYield=$t,s.unstable_wrapCallback=function(U){var Z=N;return function(){var te=N;N=Z;try{return U.apply(this,arguments)}finally{N=te}}}})(Nc)),Nc}var qd;function Pg(){return qd||(qd=1,jc.exports=Wg()),jc.exports}var Tc={exports:{}},ke={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wd;function Ig(){if(wd)return ke;wd=1;var s=zc();function f(g){var m="https://react.dev/errors/"+g;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var T=2;T<arguments.length;T++)m+="&args[]="+encodeURIComponent(arguments[T])}return"Minified React error #"+g+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(){}var r={d:{f:d,r:function(){throw Error(f(522))},D:d,C:d,L:d,m:d,X:d,S:d,M:d},p:0,findDOMNode:null},h=Symbol.for("react.portal");function p(g,m,T){var y=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:h,key:y==null?null:""+y,children:g,containerInfo:m,implementation:T}}var S=s.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function _(g,m){if(g==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return ke.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,ke.createPortal=function(g,m){var T=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(f(299));return p(g,m,null,T)},ke.flushSync=function(g){var m=S.T,T=r.p;try{if(S.T=null,r.p=2,g)return g()}finally{S.T=m,r.p=T,r.d.f()}},ke.preconnect=function(g,m){typeof g=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,r.d.C(g,m))},ke.prefetchDNS=function(g){typeof g=="string"&&r.d.D(g)},ke.preinit=function(g,m){if(typeof g=="string"&&m&&typeof m.as=="string"){var T=m.as,y=_(T,m.crossOrigin),N=typeof m.integrity=="string"?m.integrity:void 0,z=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;T==="style"?r.d.S(g,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:y,integrity:N,fetchPriority:z}):T==="script"&&r.d.X(g,{crossOrigin:y,integrity:N,fetchPriority:z,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},ke.preinitModule=function(g,m){if(typeof g=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var T=_(m.as,m.crossOrigin);r.d.M(g,{crossOrigin:T,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&r.d.M(g)},ke.preload=function(g,m){if(typeof g=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var T=m.as,y=_(T,m.crossOrigin);r.d.L(g,T,{crossOrigin:y,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},ke.preloadModule=function(g,m){if(typeof g=="string")if(m){var T=_(m.as,m.crossOrigin);r.d.m(g,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:T,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else r.d.m(g)},ke.requestFormReset=function(g){r.d.r(g)},ke.unstable_batchedUpdates=function(g,m){return g(m)},ke.useFormState=function(g,m,T){return S.H.useFormState(g,m,T)},ke.useFormStatus=function(){return S.H.useHostTransitionStatus()},ke.version="19.1.1",ke}var Yd;function ah(){if(Yd)return Tc.exports;Yd=1;function s(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(s)}catch(f){console.error(f)}}return s(),Tc.exports=Ig(),Tc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gd;function ev(){if(Gd)return Hn;Gd=1;var s=Pg(),f=zc(),d=ah();function r(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)t+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function h(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function p(e){var t=e,l=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(l=t.return),e=t.return;while(e)}return t.tag===3?l:null}function S(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function _(e){if(p(e)!==e)throw Error(r(188))}function g(e){var t=e.alternate;if(!t){if(t=p(e),t===null)throw Error(r(188));return t!==e?null:e}for(var l=e,a=t;;){var n=l.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===l)return _(n),e;if(u===a)return _(n),t;u=u.sibling}throw Error(r(188))}if(l.return!==a.return)l=n,a=u;else{for(var c=!1,o=n.child;o;){if(o===l){c=!0,l=n,a=u;break}if(o===a){c=!0,a=n,l=u;break}o=o.sibling}if(!c){for(o=u.child;o;){if(o===l){c=!0,l=u,a=n;break}if(o===a){c=!0,a=u,l=n;break}o=o.sibling}if(!c)throw Error(r(189))}}if(l.alternate!==a)throw Error(r(190))}if(l.tag!==3)throw Error(r(188));return l.stateNode.current===l?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var T=Object.assign,y=Symbol.for("react.element"),N=Symbol.for("react.transitional.element"),z=Symbol.for("react.portal"),C=Symbol.for("react.fragment"),G=Symbol.for("react.strict_mode"),M=Symbol.for("react.profiler"),L=Symbol.for("react.provider"),K=Symbol.for("react.consumer"),Q=Symbol.for("react.context"),W=Symbol.for("react.forward_ref"),w=Symbol.for("react.suspense"),fe=Symbol.for("react.suspense_list"),Ce=Symbol.for("react.memo"),Fe=Symbol.for("react.lazy"),ht=Symbol.for("react.activity"),$t=Symbol.for("react.memo_cache_sentinel"),X=Symbol.iterator;function F(e){return e===null||typeof e!="object"?null:(e=X&&e[X]||e["@@iterator"],typeof e=="function"?e:null)}var je=Symbol.for("react.client.reference");function we(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===je?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case C:return"Fragment";case M:return"Profiler";case G:return"StrictMode";case w:return"Suspense";case fe:return"SuspenseList";case ht:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case z:return"Portal";case Q:return(e.displayName||"Context")+".Provider";case K:return(e._context.displayName||"Context")+".Consumer";case W:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ce:return t=e.displayName||null,t!==null?t:we(e.type)||"Memo";case Fe:t=e._payload,e=e._init;try{return we(e(t))}catch{}}return null}var Ae=Array.isArray,U=f.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Z=d.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,te={pending:!1,data:null,method:null,action:null},xe=[],x=-1;function Y(e){return{current:e}}function J(e){0>x||(e.current=xe[x],xe[x]=null,x--)}function V(e,t){x++,xe[x]=e.current,e.current=t}var P=Y(null),oe=Y(null),ae=Y(null),at=Y(null);function Te(e,t){switch(V(ae,t),V(oe,e),V(P,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?id(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=id(t),e=sd(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}J(P),V(P,e)}function Ft(){J(P),J(oe),J(ae)}function ni(e){e.memoizedState!==null&&V(at,e);var t=P.current,l=sd(t,e.type);t!==l&&(V(oe,e),V(P,l))}function Ln(e){oe.current===e&&(J(P),J(oe)),at.current===e&&(J(at),zn._currentValue=te)}var ui=Object.prototype.hasOwnProperty,ii=s.unstable_scheduleCallback,si=s.unstable_cancelCallback,jh=s.unstable_shouldYield,Nh=s.unstable_requestPaint,Mt=s.unstable_now,Th=s.unstable_getCurrentPriorityLevel,wc=s.unstable_ImmediatePriority,Yc=s.unstable_UserBlockingPriority,Xn=s.unstable_NormalPriority,Eh=s.unstable_LowPriority,Gc=s.unstable_IdlePriority,Ah=s.log,Mh=s.unstable_setDisableYieldValue,qa=null,nt=null;function Wt(e){if(typeof Ah=="function"&&Mh(e),nt&&typeof nt.setStrictMode=="function")try{nt.setStrictMode(qa,e)}catch{}}var ut=Math.clz32?Math.clz32:zh,Oh=Math.log,Rh=Math.LN2;function zh(e){return e>>>=0,e===0?32:31-(Oh(e)/Rh|0)|0}var Qn=256,Zn=4194304;function Nl(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Vn(e,t,l){var a=e.pendingLanes;if(a===0)return 0;var n=0,u=e.suspendedLanes,c=e.pingedLanes;e=e.warmLanes;var o=a&134217727;return o!==0?(a=o&~u,a!==0?n=Nl(a):(c&=o,c!==0?n=Nl(c):l||(l=o&~e,l!==0&&(n=Nl(l))))):(o=a&~u,o!==0?n=Nl(o):c!==0?n=Nl(c):l||(l=a&~e,l!==0&&(n=Nl(l)))),n===0?0:t!==0&&t!==n&&(t&u)===0&&(u=n&-n,l=t&-t,u>=l||u===32&&(l&4194048)!==0)?t:n}function wa(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Dh(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Lc(){var e=Qn;return Qn<<=1,(Qn&4194048)===0&&(Qn=256),e}function Xc(){var e=Zn;return Zn<<=1,(Zn&62914560)===0&&(Zn=4194304),e}function ci(e){for(var t=[],l=0;31>l;l++)t.push(e);return t}function Ya(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ch(e,t,l,a,n,u){var c=e.pendingLanes;e.pendingLanes=l,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=l,e.entangledLanes&=l,e.errorRecoveryDisabledLanes&=l,e.shellSuspendCounter=0;var o=e.entanglements,v=e.expirationTimes,A=e.hiddenUpdates;for(l=c&~l;0<l;){var B=31-ut(l),q=1<<B;o[B]=0,v[B]=-1;var R=A[B];if(R!==null)for(A[B]=null,B=0;B<R.length;B++){var D=R[B];D!==null&&(D.lane&=-536870913)}l&=~q}a!==0&&Qc(e,a,0),u!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=u&~(c&~t))}function Qc(e,t,l){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-ut(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|l&4194090}function Zc(e,t){var l=e.entangledLanes|=t;for(e=e.entanglements;l;){var a=31-ut(l),n=1<<a;n&t|e[a]&t&&(e[a]|=t),l&=~n}}function ri(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function fi(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Vc(){var e=Z.p;return e!==0?e:(e=window.event,e===void 0?32:Ad(e.type))}function _h(e,t){var l=Z.p;try{return Z.p=e,t()}finally{Z.p=l}}var Pt=Math.random().toString(36).slice(2),Ke="__reactFiber$"+Pt,Pe="__reactProps$"+Pt,Fl="__reactContainer$"+Pt,oi="__reactEvents$"+Pt,Uh="__reactListeners$"+Pt,Bh="__reactHandles$"+Pt,Kc="__reactResources$"+Pt,Ga="__reactMarker$"+Pt;function di(e){delete e[Ke],delete e[Pe],delete e[oi],delete e[Uh],delete e[Bh]}function Wl(e){var t=e[Ke];if(t)return t;for(var l=e.parentNode;l;){if(t=l[Fl]||l[Ke]){if(l=t.alternate,t.child!==null||l!==null&&l.child!==null)for(e=od(e);e!==null;){if(l=e[Ke])return l;e=od(e)}return t}e=l,l=e.parentNode}return null}function Pl(e){if(e=e[Ke]||e[Fl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function La(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(r(33))}function Il(e){var t=e[Kc];return t||(t=e[Kc]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ye(e){e[Ga]=!0}var Jc=new Set,kc={};function Tl(e,t){ea(e,t),ea(e+"Capture",t)}function ea(e,t){for(kc[e]=t,e=0;e<t.length;e++)Jc.add(t[e])}var Hh=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),$c={},Fc={};function qh(e){return ui.call(Fc,e)?!0:ui.call($c,e)?!1:Hh.test(e)?Fc[e]=!0:($c[e]=!0,!1)}function Kn(e,t,l){if(qh(t))if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+l)}}function Jn(e,t,l){if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+l)}}function _t(e,t,l,a){if(a===null)e.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(l);return}e.setAttributeNS(t,l,""+a)}}var hi,Wc;function ta(e){if(hi===void 0)try{throw Error()}catch(l){var t=l.stack.trim().match(/\n( *(at )?)/);hi=t&&t[1]||"",Wc=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+hi+e+Wc}var mi=!1;function gi(e,t){if(!e||mi)return"";mi=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var q=function(){throw Error()};if(Object.defineProperty(q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(q,[])}catch(D){var R=D}Reflect.construct(e,[],q)}else{try{q.call()}catch(D){R=D}e.call(q.prototype)}}else{try{throw Error()}catch(D){R=D}(q=e())&&typeof q.catch=="function"&&q.catch(function(){})}}catch(D){if(D&&R&&typeof D.stack=="string")return[D.stack,R.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),c=u[0],o=u[1];if(c&&o){var v=c.split(`
`),A=o.split(`
`);for(n=a=0;a<v.length&&!v[a].includes("DetermineComponentFrameRoot");)a++;for(;n<A.length&&!A[n].includes("DetermineComponentFrameRoot");)n++;if(a===v.length||n===A.length)for(a=v.length-1,n=A.length-1;1<=a&&0<=n&&v[a]!==A[n];)n--;for(;1<=a&&0<=n;a--,n--)if(v[a]!==A[n]){if(a!==1||n!==1)do if(a--,n--,0>n||v[a]!==A[n]){var B=`
`+v[a].replace(" at new "," at ");return e.displayName&&B.includes("<anonymous>")&&(B=B.replace("<anonymous>",e.displayName)),B}while(1<=a&&0<=n);break}}}finally{mi=!1,Error.prepareStackTrace=l}return(l=e?e.displayName||e.name:"")?ta(l):""}function wh(e){switch(e.tag){case 26:case 27:case 5:return ta(e.type);case 16:return ta("Lazy");case 13:return ta("Suspense");case 19:return ta("SuspenseList");case 0:case 15:return gi(e.type,!1);case 11:return gi(e.type.render,!1);case 1:return gi(e.type,!0);case 31:return ta("Activity");default:return""}}function Pc(e){try{var t="";do t+=wh(e),e=e.return;while(e);return t}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function mt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ic(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Yh(e){var t=Ic(e)?"checked":"value",l=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,u=l.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(c){a=""+c,u.call(this,c)}}),Object.defineProperty(e,t,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(c){a=""+c},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function kn(e){e._valueTracker||(e._valueTracker=Yh(e))}function er(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var l=t.getValue(),a="";return e&&(a=Ic(e)?e.checked?"true":"false":e.value),e=a,e!==l?(t.setValue(e),!0):!1}function $n(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Gh=/[\n"\\]/g;function gt(e){return e.replace(Gh,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function vi(e,t,l,a,n,u,c,o){e.name="",c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"?e.type=c:e.removeAttribute("type"),t!=null?c==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+mt(t)):e.value!==""+mt(t)&&(e.value=""+mt(t)):c!=="submit"&&c!=="reset"||e.removeAttribute("value"),t!=null?pi(e,c,mt(t)):l!=null?pi(e,c,mt(l)):a!=null&&e.removeAttribute("value"),n==null&&u!=null&&(e.defaultChecked=!!u),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?e.name=""+mt(o):e.removeAttribute("name")}function tr(e,t,l,a,n,u,c,o){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(e.type=u),t!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||t!=null))return;l=l!=null?""+mt(l):"",t=t!=null?""+mt(t):l,o||t===e.value||(e.value=t),e.defaultValue=t}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=o?e.checked:!!a,e.defaultChecked=!!a,c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.name=c)}function pi(e,t,l){t==="number"&&$n(e.ownerDocument)===e||e.defaultValue===""+l||(e.defaultValue=""+l)}function la(e,t,l,a){if(e=e.options,t){t={};for(var n=0;n<l.length;n++)t["$"+l[n]]=!0;for(l=0;l<e.length;l++)n=t.hasOwnProperty("$"+e[l].value),e[l].selected!==n&&(e[l].selected=n),n&&a&&(e[l].defaultSelected=!0)}else{for(l=""+mt(l),t=null,n=0;n<e.length;n++){if(e[n].value===l){e[n].selected=!0,a&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function lr(e,t,l){if(t!=null&&(t=""+mt(t),t!==e.value&&(e.value=t),l==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=l!=null?""+mt(l):""}function ar(e,t,l,a){if(t==null){if(a!=null){if(l!=null)throw Error(r(92));if(Ae(a)){if(1<a.length)throw Error(r(93));a=a[0]}l=a}l==null&&(l=""),t=l}l=mt(t),e.defaultValue=l,a=e.textContent,a===l&&a!==""&&a!==null&&(e.value=a)}function aa(e,t){if(t){var l=e.firstChild;if(l&&l===e.lastChild&&l.nodeType===3){l.nodeValue=t;return}}e.textContent=t}var Lh=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function nr(e,t,l){var a=t.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,l):typeof l!="number"||l===0||Lh.has(t)?t==="float"?e.cssFloat=l:e[t]=(""+l).trim():e[t]=l+"px"}function ur(e,t,l){if(t!=null&&typeof t!="object")throw Error(r(62));if(e=e.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var n in t)a=t[n],t.hasOwnProperty(n)&&l[n]!==a&&nr(e,n,a)}else for(var u in t)t.hasOwnProperty(u)&&nr(e,u,t[u])}function yi(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Xh=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Qh=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Fn(e){return Qh.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var xi=null;function bi(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var na=null,ua=null;function ir(e){var t=Pl(e);if(t&&(e=t.stateNode)){var l=e[Pe]||null;e:switch(e=t.stateNode,t.type){case"input":if(vi(e,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),t=l.name,l.type==="radio"&&t!=null){for(l=e;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+gt(""+t)+'"][type="radio"]'),t=0;t<l.length;t++){var a=l[t];if(a!==e&&a.form===e.form){var n=a[Pe]||null;if(!n)throw Error(r(90));vi(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<l.length;t++)a=l[t],a.form===e.form&&er(a)}break e;case"textarea":lr(e,l.value,l.defaultValue);break e;case"select":t=l.value,t!=null&&la(e,!!l.multiple,t,!1)}}}var Si=!1;function sr(e,t,l){if(Si)return e(t,l);Si=!0;try{var a=e(t);return a}finally{if(Si=!1,(na!==null||ua!==null)&&(Bu(),na&&(t=na,e=ua,ua=na=null,ir(t),e)))for(t=0;t<e.length;t++)ir(e[t])}}function Xa(e,t){var l=e.stateNode;if(l===null)return null;var a=l[Pe]||null;if(a===null)return null;l=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(l&&typeof l!="function")throw Error(r(231,t,typeof l));return l}var Ut=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ji=!1;if(Ut)try{var Qa={};Object.defineProperty(Qa,"passive",{get:function(){ji=!0}}),window.addEventListener("test",Qa,Qa),window.removeEventListener("test",Qa,Qa)}catch{ji=!1}var It=null,Ni=null,Wn=null;function cr(){if(Wn)return Wn;var e,t=Ni,l=t.length,a,n="value"in It?It.value:It.textContent,u=n.length;for(e=0;e<l&&t[e]===n[e];e++);var c=l-e;for(a=1;a<=c&&t[l-a]===n[u-a];a++);return Wn=n.slice(e,1<a?1-a:void 0)}function Pn(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function In(){return!0}function rr(){return!1}function Ie(e){function t(l,a,n,u,c){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=c,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(l=e[o],this[o]=l?l(u):u[o]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?In:rr,this.isPropagationStopped=rr,this}return T(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=In)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=In)},persist:function(){},isPersistent:In}),t}var El={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},eu=Ie(El),Za=T({},El,{view:0,detail:0}),Zh=Ie(Za),Ti,Ei,Va,tu=T({},Za,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Mi,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Va&&(Va&&e.type==="mousemove"?(Ti=e.screenX-Va.screenX,Ei=e.screenY-Va.screenY):Ei=Ti=0,Va=e),Ti)},movementY:function(e){return"movementY"in e?e.movementY:Ei}}),fr=Ie(tu),Vh=T({},tu,{dataTransfer:0}),Kh=Ie(Vh),Jh=T({},Za,{relatedTarget:0}),Ai=Ie(Jh),kh=T({},El,{animationName:0,elapsedTime:0,pseudoElement:0}),$h=Ie(kh),Fh=T({},El,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Wh=Ie(Fh),Ph=T({},El,{data:0}),or=Ie(Ph),Ih={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},em={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},tm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function lm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=tm[e])?!!t[e]:!1}function Mi(){return lm}var am=T({},Za,{key:function(e){if(e.key){var t=Ih[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Pn(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?em[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Mi,charCode:function(e){return e.type==="keypress"?Pn(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Pn(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),nm=Ie(am),um=T({},tu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),dr=Ie(um),im=T({},Za,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Mi}),sm=Ie(im),cm=T({},El,{propertyName:0,elapsedTime:0,pseudoElement:0}),rm=Ie(cm),fm=T({},tu,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),om=Ie(fm),dm=T({},El,{newState:0,oldState:0}),hm=Ie(dm),mm=[9,13,27,32],Oi=Ut&&"CompositionEvent"in window,Ka=null;Ut&&"documentMode"in document&&(Ka=document.documentMode);var gm=Ut&&"TextEvent"in window&&!Ka,hr=Ut&&(!Oi||Ka&&8<Ka&&11>=Ka),mr=" ",gr=!1;function vr(e,t){switch(e){case"keyup":return mm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function pr(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var ia=!1;function vm(e,t){switch(e){case"compositionend":return pr(t);case"keypress":return t.which!==32?null:(gr=!0,mr);case"textInput":return e=t.data,e===mr&&gr?null:e;default:return null}}function pm(e,t){if(ia)return e==="compositionend"||!Oi&&vr(e,t)?(e=cr(),Wn=Ni=It=null,ia=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return hr&&t.locale!=="ko"?null:t.data;default:return null}}var ym={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!ym[e.type]:t==="textarea"}function xr(e,t,l,a){na?ua?ua.push(a):ua=[a]:na=a,t=Lu(t,"onChange"),0<t.length&&(l=new eu("onChange","change",null,l,a),e.push({event:l,listeners:t}))}var Ja=null,ka=null;function xm(e){td(e,0)}function lu(e){var t=La(e);if(er(t))return e}function br(e,t){if(e==="change")return t}var Sr=!1;if(Ut){var Ri;if(Ut){var zi="oninput"in document;if(!zi){var jr=document.createElement("div");jr.setAttribute("oninput","return;"),zi=typeof jr.oninput=="function"}Ri=zi}else Ri=!1;Sr=Ri&&(!document.documentMode||9<document.documentMode)}function Nr(){Ja&&(Ja.detachEvent("onpropertychange",Tr),ka=Ja=null)}function Tr(e){if(e.propertyName==="value"&&lu(ka)){var t=[];xr(t,ka,e,bi(e)),sr(xm,t)}}function bm(e,t,l){e==="focusin"?(Nr(),Ja=t,ka=l,Ja.attachEvent("onpropertychange",Tr)):e==="focusout"&&Nr()}function Sm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return lu(ka)}function jm(e,t){if(e==="click")return lu(t)}function Nm(e,t){if(e==="input"||e==="change")return lu(t)}function Tm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var it=typeof Object.is=="function"?Object.is:Tm;function $a(e,t){if(it(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var l=Object.keys(e),a=Object.keys(t);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!ui.call(t,n)||!it(e[n],t[n]))return!1}return!0}function Er(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ar(e,t){var l=Er(e);e=0;for(var a;l;){if(l.nodeType===3){if(a=e+l.textContent.length,e<=t&&a>=t)return{node:l,offset:t-e};e=a}e:{for(;l;){if(l.nextSibling){l=l.nextSibling;break e}l=l.parentNode}l=void 0}l=Er(l)}}function Mr(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Mr(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Or(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=$n(e.document);t instanceof e.HTMLIFrameElement;){try{var l=typeof t.contentWindow.location.href=="string"}catch{l=!1}if(l)e=t.contentWindow;else break;t=$n(e.document)}return t}function Di(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Em=Ut&&"documentMode"in document&&11>=document.documentMode,sa=null,Ci=null,Fa=null,_i=!1;function Rr(e,t,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;_i||sa==null||sa!==$n(a)||(a=sa,"selectionStart"in a&&Di(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Fa&&$a(Fa,a)||(Fa=a,a=Lu(Ci,"onSelect"),0<a.length&&(t=new eu("onSelect","select",null,t,l),e.push({event:t,listeners:a}),t.target=sa)))}function Al(e,t){var l={};return l[e.toLowerCase()]=t.toLowerCase(),l["Webkit"+e]="webkit"+t,l["Moz"+e]="moz"+t,l}var ca={animationend:Al("Animation","AnimationEnd"),animationiteration:Al("Animation","AnimationIteration"),animationstart:Al("Animation","AnimationStart"),transitionrun:Al("Transition","TransitionRun"),transitionstart:Al("Transition","TransitionStart"),transitioncancel:Al("Transition","TransitionCancel"),transitionend:Al("Transition","TransitionEnd")},Ui={},zr={};Ut&&(zr=document.createElement("div").style,"AnimationEvent"in window||(delete ca.animationend.animation,delete ca.animationiteration.animation,delete ca.animationstart.animation),"TransitionEvent"in window||delete ca.transitionend.transition);function Ml(e){if(Ui[e])return Ui[e];if(!ca[e])return e;var t=ca[e],l;for(l in t)if(t.hasOwnProperty(l)&&l in zr)return Ui[e]=t[l];return e}var Dr=Ml("animationend"),Cr=Ml("animationiteration"),_r=Ml("animationstart"),Am=Ml("transitionrun"),Mm=Ml("transitionstart"),Om=Ml("transitioncancel"),Ur=Ml("transitionend"),Br=new Map,Bi="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Bi.push("scrollEnd");function Nt(e,t){Br.set(e,t),Tl(t,[e])}var Hr=new WeakMap;function vt(e,t){if(typeof e=="object"&&e!==null){var l=Hr.get(e);return l!==void 0?l:(t={value:e,source:t,stack:Pc(t)},Hr.set(e,t),t)}return{value:e,source:t,stack:Pc(t)}}var pt=[],ra=0,Hi=0;function au(){for(var e=ra,t=Hi=ra=0;t<e;){var l=pt[t];pt[t++]=null;var a=pt[t];pt[t++]=null;var n=pt[t];pt[t++]=null;var u=pt[t];if(pt[t++]=null,a!==null&&n!==null){var c=a.pending;c===null?n.next=n:(n.next=c.next,c.next=n),a.pending=n}u!==0&&qr(l,n,u)}}function nu(e,t,l,a){pt[ra++]=e,pt[ra++]=t,pt[ra++]=l,pt[ra++]=a,Hi|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function qi(e,t,l,a){return nu(e,t,l,a),uu(e)}function fa(e,t){return nu(e,null,null,t),uu(e)}function qr(e,t,l){e.lanes|=l;var a=e.alternate;a!==null&&(a.lanes|=l);for(var n=!1,u=e.return;u!==null;)u.childLanes|=l,a=u.alternate,a!==null&&(a.childLanes|=l),u.tag===22&&(e=u.stateNode,e===null||e._visibility&1||(n=!0)),e=u,u=u.return;return e.tag===3?(u=e.stateNode,n&&t!==null&&(n=31-ut(l),e=u.hiddenUpdates,a=e[n],a===null?e[n]=[t]:a.push(t),t.lane=l|536870912),u):null}function uu(e){if(50<jn)throw jn=0,Qs=null,Error(r(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var oa={};function Rm(e,t,l,a){this.tag=e,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function st(e,t,l,a){return new Rm(e,t,l,a)}function wi(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Bt(e,t){var l=e.alternate;return l===null?(l=st(e.tag,t,e.key,e.mode),l.elementType=e.elementType,l.type=e.type,l.stateNode=e.stateNode,l.alternate=e,e.alternate=l):(l.pendingProps=t,l.type=e.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=e.flags&65011712,l.childLanes=e.childLanes,l.lanes=e.lanes,l.child=e.child,l.memoizedProps=e.memoizedProps,l.memoizedState=e.memoizedState,l.updateQueue=e.updateQueue,t=e.dependencies,l.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},l.sibling=e.sibling,l.index=e.index,l.ref=e.ref,l.refCleanup=e.refCleanup,l}function wr(e,t){e.flags&=65011714;var l=e.alternate;return l===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=l.childLanes,e.lanes=l.lanes,e.child=l.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=l.memoizedProps,e.memoizedState=l.memoizedState,e.updateQueue=l.updateQueue,e.type=l.type,t=l.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function iu(e,t,l,a,n,u){var c=0;if(a=e,typeof e=="function")wi(e)&&(c=1);else if(typeof e=="string")c=Dg(e,l,P.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ht:return e=st(31,l,t,n),e.elementType=ht,e.lanes=u,e;case C:return Ol(l.children,n,u,t);case G:c=8,n|=24;break;case M:return e=st(12,l,t,n|2),e.elementType=M,e.lanes=u,e;case w:return e=st(13,l,t,n),e.elementType=w,e.lanes=u,e;case fe:return e=st(19,l,t,n),e.elementType=fe,e.lanes=u,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case L:case Q:c=10;break e;case K:c=9;break e;case W:c=11;break e;case Ce:c=14;break e;case Fe:c=16,a=null;break e}c=29,l=Error(r(130,e===null?"null":typeof e,"")),a=null}return t=st(c,l,t,n),t.elementType=e,t.type=a,t.lanes=u,t}function Ol(e,t,l,a){return e=st(7,e,a,t),e.lanes=l,e}function Yi(e,t,l){return e=st(6,e,null,t),e.lanes=l,e}function Gi(e,t,l){return t=st(4,e.children!==null?e.children:[],e.key,t),t.lanes=l,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var da=[],ha=0,su=null,cu=0,yt=[],xt=0,Rl=null,Ht=1,qt="";function zl(e,t){da[ha++]=cu,da[ha++]=su,su=e,cu=t}function Yr(e,t,l){yt[xt++]=Ht,yt[xt++]=qt,yt[xt++]=Rl,Rl=e;var a=Ht;e=qt;var n=32-ut(a)-1;a&=~(1<<n),l+=1;var u=32-ut(t)+n;if(30<u){var c=n-n%5;u=(a&(1<<c)-1).toString(32),a>>=c,n-=c,Ht=1<<32-ut(t)+n|l<<n|a,qt=u+e}else Ht=1<<u|l<<n|a,qt=e}function Li(e){e.return!==null&&(zl(e,1),Yr(e,1,0))}function Xi(e){for(;e===su;)su=da[--ha],da[ha]=null,cu=da[--ha],da[ha]=null;for(;e===Rl;)Rl=yt[--xt],yt[xt]=null,qt=yt[--xt],yt[xt]=null,Ht=yt[--xt],yt[xt]=null}var We=null,Oe=null,he=!1,Dl=null,Ot=!1,Qi=Error(r(519));function Cl(e){var t=Error(r(418,""));throw Ia(vt(t,e)),Qi}function Gr(e){var t=e.stateNode,l=e.type,a=e.memoizedProps;switch(t[Ke]=e,t[Pe]=a,l){case"dialog":ce("cancel",t),ce("close",t);break;case"iframe":case"object":case"embed":ce("load",t);break;case"video":case"audio":for(l=0;l<Tn.length;l++)ce(Tn[l],t);break;case"source":ce("error",t);break;case"img":case"image":case"link":ce("error",t),ce("load",t);break;case"details":ce("toggle",t);break;case"input":ce("invalid",t),tr(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),kn(t);break;case"select":ce("invalid",t);break;case"textarea":ce("invalid",t),ar(t,a.value,a.defaultValue,a.children),kn(t)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||t.textContent===""+l||a.suppressHydrationWarning===!0||ud(t.textContent,l)?(a.popover!=null&&(ce("beforetoggle",t),ce("toggle",t)),a.onScroll!=null&&ce("scroll",t),a.onScrollEnd!=null&&ce("scrollend",t),a.onClick!=null&&(t.onclick=Xu),t=!0):t=!1,t||Cl(e)}function Lr(e){for(We=e.return;We;)switch(We.tag){case 5:case 13:Ot=!1;return;case 27:case 3:Ot=!0;return;default:We=We.return}}function Wa(e){if(e!==We)return!1;if(!he)return Lr(e),he=!0,!1;var t=e.tag,l;if((l=t!==3&&t!==27)&&((l=t===5)&&(l=e.type,l=!(l!=="form"&&l!=="button")||uc(e.type,e.memoizedProps)),l=!l),l&&Oe&&Cl(e),Lr(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(r(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(l=e.data,l==="/$"){if(t===0){Oe=Et(e.nextSibling);break e}t--}else l!=="$"&&l!=="$!"&&l!=="$?"||t++;e=e.nextSibling}Oe=null}}else t===27?(t=Oe,gl(e.type)?(e=rc,rc=null,Oe=e):Oe=t):Oe=We?Et(e.stateNode.nextSibling):null;return!0}function Pa(){Oe=We=null,he=!1}function Xr(){var e=Dl;return e!==null&&(lt===null?lt=e:lt.push.apply(lt,e),Dl=null),e}function Ia(e){Dl===null?Dl=[e]:Dl.push(e)}var Zi=Y(null),_l=null,wt=null;function el(e,t,l){V(Zi,t._currentValue),t._currentValue=l}function Yt(e){e._currentValue=Zi.current,J(Zi)}function Vi(e,t,l){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===l)break;e=e.return}}function Ki(e,t,l,a){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var u=n.dependencies;if(u!==null){var c=n.child;u=u.firstContext;e:for(;u!==null;){var o=u;u=n;for(var v=0;v<t.length;v++)if(o.context===t[v]){u.lanes|=l,o=u.alternate,o!==null&&(o.lanes|=l),Vi(u.return,l,e),a||(c=null);break e}u=o.next}}else if(n.tag===18){if(c=n.return,c===null)throw Error(r(341));c.lanes|=l,u=c.alternate,u!==null&&(u.lanes|=l),Vi(c,l,e),c=null}else c=n.child;if(c!==null)c.return=n;else for(c=n;c!==null;){if(c===e){c=null;break}if(n=c.sibling,n!==null){n.return=c.return,c=n;break}c=c.return}n=c}}function en(e,t,l,a){e=null;for(var n=t,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var c=n.alternate;if(c===null)throw Error(r(387));if(c=c.memoizedProps,c!==null){var o=n.type;it(n.pendingProps.value,c.value)||(e!==null?e.push(o):e=[o])}}else if(n===at.current){if(c=n.alternate,c===null)throw Error(r(387));c.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(zn):e=[zn])}n=n.return}e!==null&&Ki(t,e,l,a),t.flags|=262144}function ru(e){for(e=e.firstContext;e!==null;){if(!it(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ul(e){_l=e,wt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Je(e){return Qr(_l,e)}function fu(e,t){return _l===null&&Ul(e),Qr(e,t)}function Qr(e,t){var l=t._currentValue;if(t={context:t,memoizedValue:l,next:null},wt===null){if(e===null)throw Error(r(308));wt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else wt=wt.next=t;return l}var zm=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(l,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(l){return l()})}},Dm=s.unstable_scheduleCallback,Cm=s.unstable_NormalPriority,He={$$typeof:Q,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ji(){return{controller:new zm,data:new Map,refCount:0}}function tn(e){e.refCount--,e.refCount===0&&Dm(Cm,function(){e.controller.abort()})}var ln=null,ki=0,ma=0,ga=null;function _m(e,t){if(ln===null){var l=ln=[];ki=0,ma=Fs(),ga={status:"pending",value:void 0,then:function(a){l.push(a)}}}return ki++,t.then(Zr,Zr),t}function Zr(){if(--ki===0&&ln!==null){ga!==null&&(ga.status="fulfilled");var e=ln;ln=null,ma=0,ga=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Um(e,t){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var n=0;n<l.length;n++)(0,l[n])(t)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var Vr=U.S;U.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&_m(e,t),Vr!==null&&Vr(e,t)};var Bl=Y(null);function $i(){var e=Bl.current;return e!==null?e:Ne.pooledCache}function ou(e,t){t===null?V(Bl,Bl.current):V(Bl,t.pool)}function Kr(){var e=$i();return e===null?null:{parent:He._currentValue,pool:e}}var an=Error(r(460)),Jr=Error(r(474)),du=Error(r(542)),Fi={then:function(){}};function kr(e){return e=e.status,e==="fulfilled"||e==="rejected"}function hu(){}function $r(e,t,l){switch(l=e[l],l===void 0?e.push(t):l!==t&&(t.then(hu,hu),t=l),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Wr(e),e;default:if(typeof t.status=="string")t.then(hu,hu);else{if(e=Ne,e!==null&&100<e.shellSuspendCounter)throw Error(r(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=a}},function(a){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Wr(e),e}throw nn=t,an}}var nn=null;function Fr(){if(nn===null)throw Error(r(459));var e=nn;return nn=null,e}function Wr(e){if(e===an||e===du)throw Error(r(483))}var tl=!1;function Wi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Pi(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ll(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function al(e,t,l){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(me&2)!==0){var n=a.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),a.pending=t,t=uu(e),qr(e,null,l),t}return nu(e,a,t,l),uu(e)}function un(e,t,l){if(t=t.updateQueue,t!==null&&(t=t.shared,(l&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,Zc(e,l)}}function Ii(e,t){var l=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var c={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?n=u=c:u=u.next=c,l=l.next}while(l!==null);u===null?n=u=t:u=u.next=t}else n=u=t;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},e.updateQueue=l;return}e=l.lastBaseUpdate,e===null?l.firstBaseUpdate=t:e.next=t,l.lastBaseUpdate=t}var es=!1;function sn(){if(es){var e=ga;if(e!==null)throw e}}function cn(e,t,l,a){es=!1;var n=e.updateQueue;tl=!1;var u=n.firstBaseUpdate,c=n.lastBaseUpdate,o=n.shared.pending;if(o!==null){n.shared.pending=null;var v=o,A=v.next;v.next=null,c===null?u=A:c.next=A,c=v;var B=e.alternate;B!==null&&(B=B.updateQueue,o=B.lastBaseUpdate,o!==c&&(o===null?B.firstBaseUpdate=A:o.next=A,B.lastBaseUpdate=v))}if(u!==null){var q=n.baseState;c=0,B=A=v=null,o=u;do{var R=o.lane&-536870913,D=R!==o.lane;if(D?(re&R)===R:(a&R)===R){R!==0&&R===ma&&(es=!0),B!==null&&(B=B.next={lane:0,tag:o.tag,payload:o.payload,callback:null,next:null});e:{var le=e,I=o;R=t;var ye=l;switch(I.tag){case 1:if(le=I.payload,typeof le=="function"){q=le.call(ye,q,R);break e}q=le;break e;case 3:le.flags=le.flags&-65537|128;case 0:if(le=I.payload,R=typeof le=="function"?le.call(ye,q,R):le,R==null)break e;q=T({},q,R);break e;case 2:tl=!0}}R=o.callback,R!==null&&(e.flags|=64,D&&(e.flags|=8192),D=n.callbacks,D===null?n.callbacks=[R]:D.push(R))}else D={lane:R,tag:o.tag,payload:o.payload,callback:o.callback,next:null},B===null?(A=B=D,v=q):B=B.next=D,c|=R;if(o=o.next,o===null){if(o=n.shared.pending,o===null)break;D=o,o=D.next,D.next=null,n.lastBaseUpdate=D,n.shared.pending=null}}while(!0);B===null&&(v=q),n.baseState=v,n.firstBaseUpdate=A,n.lastBaseUpdate=B,u===null&&(n.shared.lanes=0),ol|=c,e.lanes=c,e.memoizedState=q}}function Pr(e,t){if(typeof e!="function")throw Error(r(191,e));e.call(t)}function Ir(e,t){var l=e.callbacks;if(l!==null)for(e.callbacks=null,e=0;e<l.length;e++)Pr(l[e],t)}var va=Y(null),mu=Y(0);function ef(e,t){e=Kt,V(mu,e),V(va,t),Kt=e|t.baseLanes}function ts(){V(mu,Kt),V(va,va.current)}function ls(){Kt=mu.current,J(va),J(mu)}var nl=0,ue=null,ve=null,_e=null,gu=!1,pa=!1,Hl=!1,vu=0,rn=0,ya=null,Bm=0;function ze(){throw Error(r(321))}function as(e,t){if(t===null)return!1;for(var l=0;l<t.length&&l<e.length;l++)if(!it(e[l],t[l]))return!1;return!0}function ns(e,t,l,a,n,u){return nl=u,ue=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,U.H=e===null||e.memoizedState===null?wf:Yf,Hl=!1,u=l(a,n),Hl=!1,pa&&(u=lf(t,l,a,n)),tf(e),u}function tf(e){U.H=ju;var t=ve!==null&&ve.next!==null;if(nl=0,_e=ve=ue=null,gu=!1,rn=0,ya=null,t)throw Error(r(300));e===null||Ge||(e=e.dependencies,e!==null&&ru(e)&&(Ge=!0))}function lf(e,t,l,a){ue=e;var n=0;do{if(pa&&(ya=null),rn=0,pa=!1,25<=n)throw Error(r(301));if(n+=1,_e=ve=null,e.updateQueue!=null){var u=e.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}U.H=Xm,u=t(l,a)}while(pa);return u}function Hm(){var e=U.H,t=e.useState()[0];return t=typeof t.then=="function"?fn(t):t,e=e.useState()[0],(ve!==null?ve.memoizedState:null)!==e&&(ue.flags|=1024),t}function us(){var e=vu!==0;return vu=0,e}function is(e,t,l){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l}function ss(e){if(gu){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}gu=!1}nl=0,_e=ve=ue=null,pa=!1,rn=vu=0,ya=null}function et(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return _e===null?ue.memoizedState=_e=e:_e=_e.next=e,_e}function Ue(){if(ve===null){var e=ue.alternate;e=e!==null?e.memoizedState:null}else e=ve.next;var t=_e===null?ue.memoizedState:_e.next;if(t!==null)_e=t,ve=e;else{if(e===null)throw ue.alternate===null?Error(r(467)):Error(r(310));ve=e,e={memoizedState:ve.memoizedState,baseState:ve.baseState,baseQueue:ve.baseQueue,queue:ve.queue,next:null},_e===null?ue.memoizedState=_e=e:_e=_e.next=e}return _e}function cs(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function fn(e){var t=rn;return rn+=1,ya===null&&(ya=[]),e=$r(ya,e,t),t=ue,(_e===null?t.memoizedState:_e.next)===null&&(t=t.alternate,U.H=t===null||t.memoizedState===null?wf:Yf),e}function pu(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return fn(e);if(e.$$typeof===Q)return Je(e)}throw Error(r(438,String(e)))}function rs(e){var t=null,l=ue.updateQueue;if(l!==null&&(t=l.memoCache),t==null){var a=ue.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),l===null&&(l=cs(),ue.updateQueue=l),l.memoCache=t,l=t.data[t.index],l===void 0)for(l=t.data[t.index]=Array(e),a=0;a<e;a++)l[a]=$t;return t.index++,l}function Gt(e,t){return typeof t=="function"?t(e):t}function yu(e){var t=Ue();return fs(t,ve,e)}function fs(e,t,l){var a=e.queue;if(a===null)throw Error(r(311));a.lastRenderedReducer=l;var n=e.baseQueue,u=a.pending;if(u!==null){if(n!==null){var c=n.next;n.next=u.next,u.next=c}t.baseQueue=n=u,a.pending=null}if(u=e.baseState,n===null)e.memoizedState=u;else{t=n.next;var o=c=null,v=null,A=t,B=!1;do{var q=A.lane&-536870913;if(q!==A.lane?(re&q)===q:(nl&q)===q){var R=A.revertLane;if(R===0)v!==null&&(v=v.next={lane:0,revertLane:0,action:A.action,hasEagerState:A.hasEagerState,eagerState:A.eagerState,next:null}),q===ma&&(B=!0);else if((nl&R)===R){A=A.next,R===ma&&(B=!0);continue}else q={lane:0,revertLane:A.revertLane,action:A.action,hasEagerState:A.hasEagerState,eagerState:A.eagerState,next:null},v===null?(o=v=q,c=u):v=v.next=q,ue.lanes|=R,ol|=R;q=A.action,Hl&&l(u,q),u=A.hasEagerState?A.eagerState:l(u,q)}else R={lane:q,revertLane:A.revertLane,action:A.action,hasEagerState:A.hasEagerState,eagerState:A.eagerState,next:null},v===null?(o=v=R,c=u):v=v.next=R,ue.lanes|=q,ol|=q;A=A.next}while(A!==null&&A!==t);if(v===null?c=u:v.next=o,!it(u,e.memoizedState)&&(Ge=!0,B&&(l=ga,l!==null)))throw l;e.memoizedState=u,e.baseState=c,e.baseQueue=v,a.lastRenderedState=u}return n===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function os(e){var t=Ue(),l=t.queue;if(l===null)throw Error(r(311));l.lastRenderedReducer=e;var a=l.dispatch,n=l.pending,u=t.memoizedState;if(n!==null){l.pending=null;var c=n=n.next;do u=e(u,c.action),c=c.next;while(c!==n);it(u,t.memoizedState)||(Ge=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),l.lastRenderedState=u}return[u,a]}function af(e,t,l){var a=ue,n=Ue(),u=he;if(u){if(l===void 0)throw Error(r(407));l=l()}else l=t();var c=!it((ve||n).memoizedState,l);c&&(n.memoizedState=l,Ge=!0),n=n.queue;var o=sf.bind(null,a,n,e);if(on(2048,8,o,[e]),n.getSnapshot!==t||c||_e!==null&&_e.memoizedState.tag&1){if(a.flags|=2048,xa(9,xu(),uf.bind(null,a,n,l,t),null),Ne===null)throw Error(r(349));u||(nl&124)!==0||nf(a,t,l)}return l}function nf(e,t,l){e.flags|=16384,e={getSnapshot:t,value:l},t=ue.updateQueue,t===null?(t=cs(),ue.updateQueue=t,t.stores=[e]):(l=t.stores,l===null?t.stores=[e]:l.push(e))}function uf(e,t,l,a){t.value=l,t.getSnapshot=a,cf(t)&&rf(e)}function sf(e,t,l){return l(function(){cf(t)&&rf(e)})}function cf(e){var t=e.getSnapshot;e=e.value;try{var l=t();return!it(e,l)}catch{return!0}}function rf(e){var t=fa(e,2);t!==null&&dt(t,e,2)}function ds(e){var t=et();if(typeof e=="function"){var l=e;if(e=l(),Hl){Wt(!0);try{l()}finally{Wt(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Gt,lastRenderedState:e},t}function ff(e,t,l,a){return e.baseState=l,fs(e,ve,typeof a=="function"?a:Gt)}function qm(e,t,l,a,n){if(Su(e))throw Error(r(485));if(e=t.action,e!==null){var u={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(c){u.listeners.push(c)}};U.T!==null?l(!0):u.isTransition=!1,a(u),l=t.pending,l===null?(u.next=t.pending=u,of(t,u)):(u.next=l.next,t.pending=l.next=u)}}function of(e,t){var l=t.action,a=t.payload,n=e.state;if(t.isTransition){var u=U.T,c={};U.T=c;try{var o=l(n,a),v=U.S;v!==null&&v(c,o),df(e,t,o)}catch(A){hs(e,t,A)}finally{U.T=u}}else try{u=l(n,a),df(e,t,u)}catch(A){hs(e,t,A)}}function df(e,t,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){hf(e,t,a)},function(a){return hs(e,t,a)}):hf(e,t,l)}function hf(e,t,l){t.status="fulfilled",t.value=l,mf(t),e.state=l,t=e.pending,t!==null&&(l=t.next,l===t?e.pending=null:(l=l.next,t.next=l,of(e,l)))}function hs(e,t,l){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=l,mf(t),t=t.next;while(t!==a)}e.action=null}function mf(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function gf(e,t){return t}function vf(e,t){if(he){var l=Ne.formState;if(l!==null){e:{var a=ue;if(he){if(Oe){t:{for(var n=Oe,u=Ot;n.nodeType!==8;){if(!u){n=null;break t}if(n=Et(n.nextSibling),n===null){n=null;break t}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){Oe=Et(n.nextSibling),a=n.data==="F!";break e}}Cl(a)}a=!1}a&&(t=l[0])}}return l=et(),l.memoizedState=l.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:gf,lastRenderedState:t},l.queue=a,l=Bf.bind(null,ue,a),a.dispatch=l,a=ds(!1),u=ys.bind(null,ue,!1,a.queue),a=et(),n={state:t,dispatch:null,action:e,pending:null},a.queue=n,l=qm.bind(null,ue,n,u,l),n.dispatch=l,a.memoizedState=e,[t,l,!1]}function pf(e){var t=Ue();return yf(t,ve,e)}function yf(e,t,l){if(t=fs(e,t,gf)[0],e=yu(Gt)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=fn(t)}catch(c){throw c===an?du:c}else a=t;t=Ue();var n=t.queue,u=n.dispatch;return l!==t.memoizedState&&(ue.flags|=2048,xa(9,xu(),wm.bind(null,n,l),null)),[a,u,e]}function wm(e,t){e.action=t}function xf(e){var t=Ue(),l=ve;if(l!==null)return yf(t,l,e);Ue(),t=t.memoizedState,l=Ue();var a=l.queue.dispatch;return l.memoizedState=e,[t,a,!1]}function xa(e,t,l,a){return e={tag:e,create:l,deps:a,inst:t,next:null},t=ue.updateQueue,t===null&&(t=cs(),ue.updateQueue=t),l=t.lastEffect,l===null?t.lastEffect=e.next=e:(a=l.next,l.next=e,e.next=a,t.lastEffect=e),e}function xu(){return{destroy:void 0,resource:void 0}}function bf(){return Ue().memoizedState}function bu(e,t,l,a){var n=et();a=a===void 0?null:a,ue.flags|=e,n.memoizedState=xa(1|t,xu(),l,a)}function on(e,t,l,a){var n=Ue();a=a===void 0?null:a;var u=n.memoizedState.inst;ve!==null&&a!==null&&as(a,ve.memoizedState.deps)?n.memoizedState=xa(t,u,l,a):(ue.flags|=e,n.memoizedState=xa(1|t,u,l,a))}function Sf(e,t){bu(8390656,8,e,t)}function jf(e,t){on(2048,8,e,t)}function Nf(e,t){return on(4,2,e,t)}function Tf(e,t){return on(4,4,e,t)}function Ef(e,t){if(typeof t=="function"){e=e();var l=t(e);return function(){typeof l=="function"?l():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Af(e,t,l){l=l!=null?l.concat([e]):null,on(4,4,Ef.bind(null,t,e),l)}function ms(){}function Mf(e,t){var l=Ue();t=t===void 0?null:t;var a=l.memoizedState;return t!==null&&as(t,a[1])?a[0]:(l.memoizedState=[e,t],e)}function Of(e,t){var l=Ue();t=t===void 0?null:t;var a=l.memoizedState;if(t!==null&&as(t,a[1]))return a[0];if(a=e(),Hl){Wt(!0);try{e()}finally{Wt(!1)}}return l.memoizedState=[a,t],a}function gs(e,t,l){return l===void 0||(nl&1073741824)!==0?e.memoizedState=t:(e.memoizedState=l,e=Co(),ue.lanes|=e,ol|=e,l)}function Rf(e,t,l,a){return it(l,t)?l:va.current!==null?(e=gs(e,l,a),it(e,t)||(Ge=!0),e):(nl&42)===0?(Ge=!0,e.memoizedState=l):(e=Co(),ue.lanes|=e,ol|=e,t)}function zf(e,t,l,a,n){var u=Z.p;Z.p=u!==0&&8>u?u:8;var c=U.T,o={};U.T=o,ys(e,!1,t,l);try{var v=n(),A=U.S;if(A!==null&&A(o,v),v!==null&&typeof v=="object"&&typeof v.then=="function"){var B=Um(v,a);dn(e,t,B,ot(e))}else dn(e,t,a,ot(e))}catch(q){dn(e,t,{then:function(){},status:"rejected",reason:q},ot())}finally{Z.p=u,U.T=c}}function Ym(){}function vs(e,t,l,a){if(e.tag!==5)throw Error(r(476));var n=Df(e).queue;zf(e,n,t,te,l===null?Ym:function(){return Cf(e),l(a)})}function Df(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:te,baseState:te,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Gt,lastRenderedState:te},next:null};var l={};return t.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Gt,lastRenderedState:l},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Cf(e){var t=Df(e).next.queue;dn(e,t,{},ot())}function ps(){return Je(zn)}function _f(){return Ue().memoizedState}function Uf(){return Ue().memoizedState}function Gm(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var l=ot();e=ll(l);var a=al(t,e,l);a!==null&&(dt(a,t,l),un(a,t,l)),t={cache:Ji()},e.payload=t;return}t=t.return}}function Lm(e,t,l){var a=ot();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Su(e)?Hf(t,l):(l=qi(e,t,l,a),l!==null&&(dt(l,e,a),qf(l,t,a)))}function Bf(e,t,l){var a=ot();dn(e,t,l,a)}function dn(e,t,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Su(e))Hf(t,n);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var c=t.lastRenderedState,o=u(c,l);if(n.hasEagerState=!0,n.eagerState=o,it(o,c))return nu(e,t,n,0),Ne===null&&au(),!1}catch{}finally{}if(l=qi(e,t,n,a),l!==null)return dt(l,e,a),qf(l,t,a),!0}return!1}function ys(e,t,l,a){if(a={lane:2,revertLane:Fs(),action:a,hasEagerState:!1,eagerState:null,next:null},Su(e)){if(t)throw Error(r(479))}else t=qi(e,l,a,2),t!==null&&dt(t,e,2)}function Su(e){var t=e.alternate;return e===ue||t!==null&&t===ue}function Hf(e,t){pa=gu=!0;var l=e.pending;l===null?t.next=t:(t.next=l.next,l.next=t),e.pending=t}function qf(e,t,l){if((l&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,Zc(e,l)}}var ju={readContext:Je,use:pu,useCallback:ze,useContext:ze,useEffect:ze,useImperativeHandle:ze,useLayoutEffect:ze,useInsertionEffect:ze,useMemo:ze,useReducer:ze,useRef:ze,useState:ze,useDebugValue:ze,useDeferredValue:ze,useTransition:ze,useSyncExternalStore:ze,useId:ze,useHostTransitionStatus:ze,useFormState:ze,useActionState:ze,useOptimistic:ze,useMemoCache:ze,useCacheRefresh:ze},wf={readContext:Je,use:pu,useCallback:function(e,t){return et().memoizedState=[e,t===void 0?null:t],e},useContext:Je,useEffect:Sf,useImperativeHandle:function(e,t,l){l=l!=null?l.concat([e]):null,bu(4194308,4,Ef.bind(null,t,e),l)},useLayoutEffect:function(e,t){return bu(4194308,4,e,t)},useInsertionEffect:function(e,t){bu(4,2,e,t)},useMemo:function(e,t){var l=et();t=t===void 0?null:t;var a=e();if(Hl){Wt(!0);try{e()}finally{Wt(!1)}}return l.memoizedState=[a,t],a},useReducer:function(e,t,l){var a=et();if(l!==void 0){var n=l(t);if(Hl){Wt(!0);try{l(t)}finally{Wt(!1)}}}else n=t;return a.memoizedState=a.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},a.queue=e,e=e.dispatch=Lm.bind(null,ue,e),[a.memoizedState,e]},useRef:function(e){var t=et();return e={current:e},t.memoizedState=e},useState:function(e){e=ds(e);var t=e.queue,l=Bf.bind(null,ue,t);return t.dispatch=l,[e.memoizedState,l]},useDebugValue:ms,useDeferredValue:function(e,t){var l=et();return gs(l,e,t)},useTransition:function(){var e=ds(!1);return e=zf.bind(null,ue,e.queue,!0,!1),et().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,l){var a=ue,n=et();if(he){if(l===void 0)throw Error(r(407));l=l()}else{if(l=t(),Ne===null)throw Error(r(349));(re&124)!==0||nf(a,t,l)}n.memoizedState=l;var u={value:l,getSnapshot:t};return n.queue=u,Sf(sf.bind(null,a,u,e),[e]),a.flags|=2048,xa(9,xu(),uf.bind(null,a,u,l,t),null),l},useId:function(){var e=et(),t=Ne.identifierPrefix;if(he){var l=qt,a=Ht;l=(a&~(1<<32-ut(a)-1)).toString(32)+l,t="«"+t+"R"+l,l=vu++,0<l&&(t+="H"+l.toString(32)),t+="»"}else l=Bm++,t="«"+t+"r"+l.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:ps,useFormState:vf,useActionState:vf,useOptimistic:function(e){var t=et();t.memoizedState=t.baseState=e;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=l,t=ys.bind(null,ue,!0,l),l.dispatch=t,[e,t]},useMemoCache:rs,useCacheRefresh:function(){return et().memoizedState=Gm.bind(null,ue)}},Yf={readContext:Je,use:pu,useCallback:Mf,useContext:Je,useEffect:jf,useImperativeHandle:Af,useInsertionEffect:Nf,useLayoutEffect:Tf,useMemo:Of,useReducer:yu,useRef:bf,useState:function(){return yu(Gt)},useDebugValue:ms,useDeferredValue:function(e,t){var l=Ue();return Rf(l,ve.memoizedState,e,t)},useTransition:function(){var e=yu(Gt)[0],t=Ue().memoizedState;return[typeof e=="boolean"?e:fn(e),t]},useSyncExternalStore:af,useId:_f,useHostTransitionStatus:ps,useFormState:pf,useActionState:pf,useOptimistic:function(e,t){var l=Ue();return ff(l,ve,e,t)},useMemoCache:rs,useCacheRefresh:Uf},Xm={readContext:Je,use:pu,useCallback:Mf,useContext:Je,useEffect:jf,useImperativeHandle:Af,useInsertionEffect:Nf,useLayoutEffect:Tf,useMemo:Of,useReducer:os,useRef:bf,useState:function(){return os(Gt)},useDebugValue:ms,useDeferredValue:function(e,t){var l=Ue();return ve===null?gs(l,e,t):Rf(l,ve.memoizedState,e,t)},useTransition:function(){var e=os(Gt)[0],t=Ue().memoizedState;return[typeof e=="boolean"?e:fn(e),t]},useSyncExternalStore:af,useId:_f,useHostTransitionStatus:ps,useFormState:xf,useActionState:xf,useOptimistic:function(e,t){var l=Ue();return ve!==null?ff(l,ve,e,t):(l.baseState=e,[e,l.queue.dispatch])},useMemoCache:rs,useCacheRefresh:Uf},ba=null,hn=0;function Nu(e){var t=hn;return hn+=1,ba===null&&(ba=[]),$r(ba,e,t)}function mn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Tu(e,t){throw t.$$typeof===y?Error(r(525)):(e=Object.prototype.toString.call(t),Error(r(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Gf(e){var t=e._init;return t(e._payload)}function Lf(e){function t(j,b){if(e){var E=j.deletions;E===null?(j.deletions=[b],j.flags|=16):E.push(b)}}function l(j,b){if(!e)return null;for(;b!==null;)t(j,b),b=b.sibling;return null}function a(j){for(var b=new Map;j!==null;)j.key!==null?b.set(j.key,j):b.set(j.index,j),j=j.sibling;return b}function n(j,b){return j=Bt(j,b),j.index=0,j.sibling=null,j}function u(j,b,E){return j.index=E,e?(E=j.alternate,E!==null?(E=E.index,E<b?(j.flags|=67108866,b):E):(j.flags|=67108866,b)):(j.flags|=1048576,b)}function c(j){return e&&j.alternate===null&&(j.flags|=67108866),j}function o(j,b,E,H){return b===null||b.tag!==6?(b=Yi(E,j.mode,H),b.return=j,b):(b=n(b,E),b.return=j,b)}function v(j,b,E,H){var k=E.type;return k===C?B(j,b,E.props.children,H,E.key):b!==null&&(b.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===Fe&&Gf(k)===b.type)?(b=n(b,E.props),mn(b,E),b.return=j,b):(b=iu(E.type,E.key,E.props,null,j.mode,H),mn(b,E),b.return=j,b)}function A(j,b,E,H){return b===null||b.tag!==4||b.stateNode.containerInfo!==E.containerInfo||b.stateNode.implementation!==E.implementation?(b=Gi(E,j.mode,H),b.return=j,b):(b=n(b,E.children||[]),b.return=j,b)}function B(j,b,E,H,k){return b===null||b.tag!==7?(b=Ol(E,j.mode,H,k),b.return=j,b):(b=n(b,E),b.return=j,b)}function q(j,b,E){if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return b=Yi(""+b,j.mode,E),b.return=j,b;if(typeof b=="object"&&b!==null){switch(b.$$typeof){case N:return E=iu(b.type,b.key,b.props,null,j.mode,E),mn(E,b),E.return=j,E;case z:return b=Gi(b,j.mode,E),b.return=j,b;case Fe:var H=b._init;return b=H(b._payload),q(j,b,E)}if(Ae(b)||F(b))return b=Ol(b,j.mode,E,null),b.return=j,b;if(typeof b.then=="function")return q(j,Nu(b),E);if(b.$$typeof===Q)return q(j,fu(j,b),E);Tu(j,b)}return null}function R(j,b,E,H){var k=b!==null?b.key:null;if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return k!==null?null:o(j,b,""+E,H);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case N:return E.key===k?v(j,b,E,H):null;case z:return E.key===k?A(j,b,E,H):null;case Fe:return k=E._init,E=k(E._payload),R(j,b,E,H)}if(Ae(E)||F(E))return k!==null?null:B(j,b,E,H,null);if(typeof E.then=="function")return R(j,b,Nu(E),H);if(E.$$typeof===Q)return R(j,b,fu(j,E),H);Tu(j,E)}return null}function D(j,b,E,H,k){if(typeof H=="string"&&H!==""||typeof H=="number"||typeof H=="bigint")return j=j.get(E)||null,o(b,j,""+H,k);if(typeof H=="object"&&H!==null){switch(H.$$typeof){case N:return j=j.get(H.key===null?E:H.key)||null,v(b,j,H,k);case z:return j=j.get(H.key===null?E:H.key)||null,A(b,j,H,k);case Fe:var ie=H._init;return H=ie(H._payload),D(j,b,E,H,k)}if(Ae(H)||F(H))return j=j.get(E)||null,B(b,j,H,k,null);if(typeof H.then=="function")return D(j,b,E,Nu(H),k);if(H.$$typeof===Q)return D(j,b,E,fu(b,H),k);Tu(b,H)}return null}function le(j,b,E,H){for(var k=null,ie=null,$=b,ee=b=0,Xe=null;$!==null&&ee<E.length;ee++){$.index>ee?(Xe=$,$=null):Xe=$.sibling;var de=R(j,$,E[ee],H);if(de===null){$===null&&($=Xe);break}e&&$&&de.alternate===null&&t(j,$),b=u(de,b,ee),ie===null?k=de:ie.sibling=de,ie=de,$=Xe}if(ee===E.length)return l(j,$),he&&zl(j,ee),k;if($===null){for(;ee<E.length;ee++)$=q(j,E[ee],H),$!==null&&(b=u($,b,ee),ie===null?k=$:ie.sibling=$,ie=$);return he&&zl(j,ee),k}for($=a($);ee<E.length;ee++)Xe=D($,j,ee,E[ee],H),Xe!==null&&(e&&Xe.alternate!==null&&$.delete(Xe.key===null?ee:Xe.key),b=u(Xe,b,ee),ie===null?k=Xe:ie.sibling=Xe,ie=Xe);return e&&$.forEach(function(bl){return t(j,bl)}),he&&zl(j,ee),k}function I(j,b,E,H){if(E==null)throw Error(r(151));for(var k=null,ie=null,$=b,ee=b=0,Xe=null,de=E.next();$!==null&&!de.done;ee++,de=E.next()){$.index>ee?(Xe=$,$=null):Xe=$.sibling;var bl=R(j,$,de.value,H);if(bl===null){$===null&&($=Xe);break}e&&$&&bl.alternate===null&&t(j,$),b=u(bl,b,ee),ie===null?k=bl:ie.sibling=bl,ie=bl,$=Xe}if(de.done)return l(j,$),he&&zl(j,ee),k;if($===null){for(;!de.done;ee++,de=E.next())de=q(j,de.value,H),de!==null&&(b=u(de,b,ee),ie===null?k=de:ie.sibling=de,ie=de);return he&&zl(j,ee),k}for($=a($);!de.done;ee++,de=E.next())de=D($,j,ee,de.value,H),de!==null&&(e&&de.alternate!==null&&$.delete(de.key===null?ee:de.key),b=u(de,b,ee),ie===null?k=de:ie.sibling=de,ie=de);return e&&$.forEach(function(Qg){return t(j,Qg)}),he&&zl(j,ee),k}function ye(j,b,E,H){if(typeof E=="object"&&E!==null&&E.type===C&&E.key===null&&(E=E.props.children),typeof E=="object"&&E!==null){switch(E.$$typeof){case N:e:{for(var k=E.key;b!==null;){if(b.key===k){if(k=E.type,k===C){if(b.tag===7){l(j,b.sibling),H=n(b,E.props.children),H.return=j,j=H;break e}}else if(b.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===Fe&&Gf(k)===b.type){l(j,b.sibling),H=n(b,E.props),mn(H,E),H.return=j,j=H;break e}l(j,b);break}else t(j,b);b=b.sibling}E.type===C?(H=Ol(E.props.children,j.mode,H,E.key),H.return=j,j=H):(H=iu(E.type,E.key,E.props,null,j.mode,H),mn(H,E),H.return=j,j=H)}return c(j);case z:e:{for(k=E.key;b!==null;){if(b.key===k)if(b.tag===4&&b.stateNode.containerInfo===E.containerInfo&&b.stateNode.implementation===E.implementation){l(j,b.sibling),H=n(b,E.children||[]),H.return=j,j=H;break e}else{l(j,b);break}else t(j,b);b=b.sibling}H=Gi(E,j.mode,H),H.return=j,j=H}return c(j);case Fe:return k=E._init,E=k(E._payload),ye(j,b,E,H)}if(Ae(E))return le(j,b,E,H);if(F(E)){if(k=F(E),typeof k!="function")throw Error(r(150));return E=k.call(E),I(j,b,E,H)}if(typeof E.then=="function")return ye(j,b,Nu(E),H);if(E.$$typeof===Q)return ye(j,b,fu(j,E),H);Tu(j,E)}return typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint"?(E=""+E,b!==null&&b.tag===6?(l(j,b.sibling),H=n(b,E),H.return=j,j=H):(l(j,b),H=Yi(E,j.mode,H),H.return=j,j=H),c(j)):l(j,b)}return function(j,b,E,H){try{hn=0;var k=ye(j,b,E,H);return ba=null,k}catch($){if($===an||$===du)throw $;var ie=st(29,$,null,j.mode);return ie.lanes=H,ie.return=j,ie}finally{}}}var Sa=Lf(!0),Xf=Lf(!1),bt=Y(null),Rt=null;function ul(e){var t=e.alternate;V(qe,qe.current&1),V(bt,e),Rt===null&&(t===null||va.current!==null||t.memoizedState!==null)&&(Rt=e)}function Qf(e){if(e.tag===22){if(V(qe,qe.current),V(bt,e),Rt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Rt=e)}}else il()}function il(){V(qe,qe.current),V(bt,bt.current)}function Lt(e){J(bt),Rt===e&&(Rt=null),J(qe)}var qe=Y(0);function Eu(e){for(var t=e;t!==null;){if(t.tag===13){var l=t.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||cc(l)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function xs(e,t,l,a){t=e.memoizedState,l=l(a,t),l=l==null?t:T({},t,l),e.memoizedState=l,e.lanes===0&&(e.updateQueue.baseState=l)}var bs={enqueueSetState:function(e,t,l){e=e._reactInternals;var a=ot(),n=ll(a);n.payload=t,l!=null&&(n.callback=l),t=al(e,n,a),t!==null&&(dt(t,e,a),un(t,e,a))},enqueueReplaceState:function(e,t,l){e=e._reactInternals;var a=ot(),n=ll(a);n.tag=1,n.payload=t,l!=null&&(n.callback=l),t=al(e,n,a),t!==null&&(dt(t,e,a),un(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var l=ot(),a=ll(l);a.tag=2,t!=null&&(a.callback=t),t=al(e,a,l),t!==null&&(dt(t,e,l),un(t,e,l))}};function Zf(e,t,l,a,n,u,c){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,u,c):t.prototype&&t.prototype.isPureReactComponent?!$a(l,a)||!$a(n,u):!0}function Vf(e,t,l,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(l,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(l,a),t.state!==e&&bs.enqueueReplaceState(t,t.state,null)}function ql(e,t){var l=t;if("ref"in t){l={};for(var a in t)a!=="ref"&&(l[a]=t[a])}if(e=e.defaultProps){l===t&&(l=T({},l));for(var n in e)l[n]===void 0&&(l[n]=e[n])}return l}var Au=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Kf(e){Au(e)}function Jf(e){console.error(e)}function kf(e){Au(e)}function Mu(e,t){try{var l=e.onUncaughtError;l(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function $f(e,t,l){try{var a=e.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function Ss(e,t,l){return l=ll(l),l.tag=3,l.payload={element:null},l.callback=function(){Mu(e,t)},l}function Ff(e){return e=ll(e),e.tag=3,e}function Wf(e,t,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;e.payload=function(){return n(u)},e.callback=function(){$f(t,l,a)}}var c=l.stateNode;c!==null&&typeof c.componentDidCatch=="function"&&(e.callback=function(){$f(t,l,a),typeof n!="function"&&(dl===null?dl=new Set([this]):dl.add(this));var o=a.stack;this.componentDidCatch(a.value,{componentStack:o!==null?o:""})})}function Qm(e,t,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=l.alternate,t!==null&&en(t,l,n,!0),l=bt.current,l!==null){switch(l.tag){case 13:return Rt===null?Vs():l.alternate===null&&Re===0&&(Re=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===Fi?l.flags|=16384:(t=l.updateQueue,t===null?l.updateQueue=new Set([a]):t.add(a),Js(e,a,n)),!1;case 22:return l.flags|=65536,a===Fi?l.flags|=16384:(t=l.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=t):(l=t.retryQueue,l===null?t.retryQueue=new Set([a]):l.add(a)),Js(e,a,n)),!1}throw Error(r(435,l.tag))}return Js(e,a,n),Vs(),!1}if(he)return t=bt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,a!==Qi&&(e=Error(r(422),{cause:a}),Ia(vt(e,l)))):(a!==Qi&&(t=Error(r(423),{cause:a}),Ia(vt(t,l))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,a=vt(a,l),n=Ss(e.stateNode,a,n),Ii(e,n),Re!==4&&(Re=2)),!1;var u=Error(r(520),{cause:a});if(u=vt(u,l),Sn===null?Sn=[u]:Sn.push(u),Re!==4&&(Re=2),t===null)return!0;a=vt(a,l),l=t;do{switch(l.tag){case 3:return l.flags|=65536,e=n&-n,l.lanes|=e,e=Ss(l.stateNode,a,e),Ii(l,e),!1;case 1:if(t=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(dl===null||!dl.has(u))))return l.flags|=65536,n&=-n,l.lanes|=n,n=Ff(n),Wf(n,e,l,a),Ii(l,n),!1}l=l.return}while(l!==null);return!1}var Pf=Error(r(461)),Ge=!1;function Qe(e,t,l,a){t.child=e===null?Xf(t,null,l,a):Sa(t,e.child,l,a)}function If(e,t,l,a,n){l=l.render;var u=t.ref;if("ref"in a){var c={};for(var o in a)o!=="ref"&&(c[o]=a[o])}else c=a;return Ul(t),a=ns(e,t,l,c,u,n),o=us(),e!==null&&!Ge?(is(e,t,n),Xt(e,t,n)):(he&&o&&Li(t),t.flags|=1,Qe(e,t,a,n),t.child)}function eo(e,t,l,a,n){if(e===null){var u=l.type;return typeof u=="function"&&!wi(u)&&u.defaultProps===void 0&&l.compare===null?(t.tag=15,t.type=u,to(e,t,u,a,n)):(e=iu(l.type,null,a,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,!Rs(e,n)){var c=u.memoizedProps;if(l=l.compare,l=l!==null?l:$a,l(c,a)&&e.ref===t.ref)return Xt(e,t,n)}return t.flags|=1,e=Bt(u,a),e.ref=t.ref,e.return=t,t.child=e}function to(e,t,l,a,n){if(e!==null){var u=e.memoizedProps;if($a(u,a)&&e.ref===t.ref)if(Ge=!1,t.pendingProps=a=u,Rs(e,n))(e.flags&131072)!==0&&(Ge=!0);else return t.lanes=e.lanes,Xt(e,t,n)}return js(e,t,l,a,n)}function lo(e,t,l){var a=t.pendingProps,n=a.children,u=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=u!==null?u.baseLanes|l:l,e!==null){for(n=t.child=e.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;t.childLanes=u&~a}else t.childLanes=0,t.child=null;return ao(e,t,a,l)}if((l&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&ou(t,u!==null?u.cachePool:null),u!==null?ef(t,u):ts(),Qf(t);else return t.lanes=t.childLanes=536870912,ao(e,t,u!==null?u.baseLanes|l:l,l)}else u!==null?(ou(t,u.cachePool),ef(t,u),il(),t.memoizedState=null):(e!==null&&ou(t,null),ts(),il());return Qe(e,t,n,l),t.child}function ao(e,t,l,a){var n=$i();return n=n===null?null:{parent:He._currentValue,pool:n},t.memoizedState={baseLanes:l,cachePool:n},e!==null&&ou(t,null),ts(),Qf(t),e!==null&&en(e,t,a,!0),null}function Ou(e,t){var l=t.ref;if(l===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(r(284));(e===null||e.ref!==l)&&(t.flags|=4194816)}}function js(e,t,l,a,n){return Ul(t),l=ns(e,t,l,a,void 0,n),a=us(),e!==null&&!Ge?(is(e,t,n),Xt(e,t,n)):(he&&a&&Li(t),t.flags|=1,Qe(e,t,l,n),t.child)}function no(e,t,l,a,n,u){return Ul(t),t.updateQueue=null,l=lf(t,a,l,n),tf(e),a=us(),e!==null&&!Ge?(is(e,t,u),Xt(e,t,u)):(he&&a&&Li(t),t.flags|=1,Qe(e,t,l,u),t.child)}function uo(e,t,l,a,n){if(Ul(t),t.stateNode===null){var u=oa,c=l.contextType;typeof c=="object"&&c!==null&&(u=Je(c)),u=new l(a,u),t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=bs,t.stateNode=u,u._reactInternals=t,u=t.stateNode,u.props=a,u.state=t.memoizedState,u.refs={},Wi(t),c=l.contextType,u.context=typeof c=="object"&&c!==null?Je(c):oa,u.state=t.memoizedState,c=l.getDerivedStateFromProps,typeof c=="function"&&(xs(t,l,c,a),u.state=t.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(c=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),c!==u.state&&bs.enqueueReplaceState(u,u.state,null),cn(t,a,u,n),sn(),u.state=t.memoizedState),typeof u.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){u=t.stateNode;var o=t.memoizedProps,v=ql(l,o);u.props=v;var A=u.context,B=l.contextType;c=oa,typeof B=="object"&&B!==null&&(c=Je(B));var q=l.getDerivedStateFromProps;B=typeof q=="function"||typeof u.getSnapshotBeforeUpdate=="function",o=t.pendingProps!==o,B||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(o||A!==c)&&Vf(t,u,a,c),tl=!1;var R=t.memoizedState;u.state=R,cn(t,a,u,n),sn(),A=t.memoizedState,o||R!==A||tl?(typeof q=="function"&&(xs(t,l,q,a),A=t.memoizedState),(v=tl||Zf(t,l,v,a,R,A,c))?(B||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(t.flags|=4194308)):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=A),u.props=a,u.state=A,u.context=c,a=v):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{u=t.stateNode,Pi(e,t),c=t.memoizedProps,B=ql(l,c),u.props=B,q=t.pendingProps,R=u.context,A=l.contextType,v=oa,typeof A=="object"&&A!==null&&(v=Je(A)),o=l.getDerivedStateFromProps,(A=typeof o=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(c!==q||R!==v)&&Vf(t,u,a,v),tl=!1,R=t.memoizedState,u.state=R,cn(t,a,u,n),sn();var D=t.memoizedState;c!==q||R!==D||tl||e!==null&&e.dependencies!==null&&ru(e.dependencies)?(typeof o=="function"&&(xs(t,l,o,a),D=t.memoizedState),(B=tl||Zf(t,l,B,a,R,D,v)||e!==null&&e.dependencies!==null&&ru(e.dependencies))?(A||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,D,v),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,D,v)),typeof u.componentDidUpdate=="function"&&(t.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof u.componentDidUpdate!="function"||c===e.memoizedProps&&R===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||c===e.memoizedProps&&R===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=D),u.props=a,u.state=D,u.context=v,a=B):(typeof u.componentDidUpdate!="function"||c===e.memoizedProps&&R===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||c===e.memoizedProps&&R===e.memoizedState||(t.flags|=1024),a=!1)}return u=a,Ou(e,t),a=(t.flags&128)!==0,u||a?(u=t.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:u.render(),t.flags|=1,e!==null&&a?(t.child=Sa(t,e.child,null,n),t.child=Sa(t,null,l,n)):Qe(e,t,l,n),t.memoizedState=u.state,e=t.child):e=Xt(e,t,n),e}function io(e,t,l,a){return Pa(),t.flags|=256,Qe(e,t,l,a),t.child}var Ns={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ts(e){return{baseLanes:e,cachePool:Kr()}}function Es(e,t,l){return e=e!==null?e.childLanes&~l:0,t&&(e|=St),e}function so(e,t,l){var a=t.pendingProps,n=!1,u=(t.flags&128)!==0,c;if((c=u)||(c=e!==null&&e.memoizedState===null?!1:(qe.current&2)!==0),c&&(n=!0,t.flags&=-129),c=(t.flags&32)!==0,t.flags&=-33,e===null){if(he){if(n?ul(t):il(),he){var o=Oe,v;if(v=o){e:{for(v=o,o=Ot;v.nodeType!==8;){if(!o){o=null;break e}if(v=Et(v.nextSibling),v===null){o=null;break e}}o=v}o!==null?(t.memoizedState={dehydrated:o,treeContext:Rl!==null?{id:Ht,overflow:qt}:null,retryLane:536870912,hydrationErrors:null},v=st(18,null,null,0),v.stateNode=o,v.return=t,t.child=v,We=t,Oe=null,v=!0):v=!1}v||Cl(t)}if(o=t.memoizedState,o!==null&&(o=o.dehydrated,o!==null))return cc(o)?t.lanes=32:t.lanes=536870912,null;Lt(t)}return o=a.children,a=a.fallback,n?(il(),n=t.mode,o=Ru({mode:"hidden",children:o},n),a=Ol(a,n,l,null),o.return=t,a.return=t,o.sibling=a,t.child=o,n=t.child,n.memoizedState=Ts(l),n.childLanes=Es(e,c,l),t.memoizedState=Ns,a):(ul(t),As(t,o))}if(v=e.memoizedState,v!==null&&(o=v.dehydrated,o!==null)){if(u)t.flags&256?(ul(t),t.flags&=-257,t=Ms(e,t,l)):t.memoizedState!==null?(il(),t.child=e.child,t.flags|=128,t=null):(il(),n=a.fallback,o=t.mode,a=Ru({mode:"visible",children:a.children},o),n=Ol(n,o,l,null),n.flags|=2,a.return=t,n.return=t,a.sibling=n,t.child=a,Sa(t,e.child,null,l),a=t.child,a.memoizedState=Ts(l),a.childLanes=Es(e,c,l),t.memoizedState=Ns,t=n);else if(ul(t),cc(o)){if(c=o.nextSibling&&o.nextSibling.dataset,c)var A=c.dgst;c=A,a=Error(r(419)),a.stack="",a.digest=c,Ia({value:a,source:null,stack:null}),t=Ms(e,t,l)}else if(Ge||en(e,t,l,!1),c=(l&e.childLanes)!==0,Ge||c){if(c=Ne,c!==null&&(a=l&-l,a=(a&42)!==0?1:ri(a),a=(a&(c.suspendedLanes|l))!==0?0:a,a!==0&&a!==v.retryLane))throw v.retryLane=a,fa(e,a),dt(c,e,a),Pf;o.data==="$?"||Vs(),t=Ms(e,t,l)}else o.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=v.treeContext,Oe=Et(o.nextSibling),We=t,he=!0,Dl=null,Ot=!1,e!==null&&(yt[xt++]=Ht,yt[xt++]=qt,yt[xt++]=Rl,Ht=e.id,qt=e.overflow,Rl=t),t=As(t,a.children),t.flags|=4096);return t}return n?(il(),n=a.fallback,o=t.mode,v=e.child,A=v.sibling,a=Bt(v,{mode:"hidden",children:a.children}),a.subtreeFlags=v.subtreeFlags&65011712,A!==null?n=Bt(A,n):(n=Ol(n,o,l,null),n.flags|=2),n.return=t,a.return=t,a.sibling=n,t.child=a,a=n,n=t.child,o=e.child.memoizedState,o===null?o=Ts(l):(v=o.cachePool,v!==null?(A=He._currentValue,v=v.parent!==A?{parent:A,pool:A}:v):v=Kr(),o={baseLanes:o.baseLanes|l,cachePool:v}),n.memoizedState=o,n.childLanes=Es(e,c,l),t.memoizedState=Ns,a):(ul(t),l=e.child,e=l.sibling,l=Bt(l,{mode:"visible",children:a.children}),l.return=t,l.sibling=null,e!==null&&(c=t.deletions,c===null?(t.deletions=[e],t.flags|=16):c.push(e)),t.child=l,t.memoizedState=null,l)}function As(e,t){return t=Ru({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Ru(e,t){return e=st(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Ms(e,t,l){return Sa(t,e.child,null,l),e=As(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function co(e,t,l){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),Vi(e.return,t,l)}function Os(e,t,l,a,n){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=l,u.tailMode=n)}function ro(e,t,l){var a=t.pendingProps,n=a.revealOrder,u=a.tail;if(Qe(e,t,a.children,l),a=qe.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&co(e,l,t);else if(e.tag===19)co(e,l,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(V(qe,a),n){case"forwards":for(l=t.child,n=null;l!==null;)e=l.alternate,e!==null&&Eu(e)===null&&(n=l),l=l.sibling;l=n,l===null?(n=t.child,t.child=null):(n=l.sibling,l.sibling=null),Os(t,!1,n,l,u);break;case"backwards":for(l=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&Eu(e)===null){t.child=n;break}e=n.sibling,n.sibling=l,l=n,n=e}Os(t,!0,l,null,u);break;case"together":Os(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Xt(e,t,l){if(e!==null&&(t.dependencies=e.dependencies),ol|=t.lanes,(l&t.childLanes)===0)if(e!==null){if(en(e,t,l,!1),(l&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(r(153));if(t.child!==null){for(e=t.child,l=Bt(e,e.pendingProps),t.child=l,l.return=t;e.sibling!==null;)e=e.sibling,l=l.sibling=Bt(e,e.pendingProps),l.return=t;l.sibling=null}return t.child}function Rs(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&ru(e)))}function Zm(e,t,l){switch(t.tag){case 3:Te(t,t.stateNode.containerInfo),el(t,He,e.memoizedState.cache),Pa();break;case 27:case 5:ni(t);break;case 4:Te(t,t.stateNode.containerInfo);break;case 10:el(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(ul(t),t.flags|=128,null):(l&t.child.childLanes)!==0?so(e,t,l):(ul(t),e=Xt(e,t,l),e!==null?e.sibling:null);ul(t);break;case 19:var n=(e.flags&128)!==0;if(a=(l&t.childLanes)!==0,a||(en(e,t,l,!1),a=(l&t.childLanes)!==0),n){if(a)return ro(e,t,l);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),V(qe,qe.current),a)break;return null;case 22:case 23:return t.lanes=0,lo(e,t,l);case 24:el(t,He,e.memoizedState.cache)}return Xt(e,t,l)}function fo(e,t,l){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ge=!0;else{if(!Rs(e,l)&&(t.flags&128)===0)return Ge=!1,Zm(e,t,l);Ge=(e.flags&131072)!==0}else Ge=!1,he&&(t.flags&1048576)!==0&&Yr(t,cu,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,n=a._init;if(a=n(a._payload),t.type=a,typeof a=="function")wi(a)?(e=ql(a,e),t.tag=1,t=uo(null,t,a,e,l)):(t.tag=0,t=js(null,t,a,e,l));else{if(a!=null){if(n=a.$$typeof,n===W){t.tag=11,t=If(null,t,a,e,l);break e}else if(n===Ce){t.tag=14,t=eo(null,t,a,e,l);break e}}throw t=we(a)||a,Error(r(306,t,""))}}return t;case 0:return js(e,t,t.type,t.pendingProps,l);case 1:return a=t.type,n=ql(a,t.pendingProps),uo(e,t,a,n,l);case 3:e:{if(Te(t,t.stateNode.containerInfo),e===null)throw Error(r(387));a=t.pendingProps;var u=t.memoizedState;n=u.element,Pi(e,t),cn(t,a,null,l);var c=t.memoizedState;if(a=c.cache,el(t,He,a),a!==u.cache&&Ki(t,[He],l,!0),sn(),a=c.element,u.isDehydrated)if(u={element:a,isDehydrated:!1,cache:c.cache},t.updateQueue.baseState=u,t.memoizedState=u,t.flags&256){t=io(e,t,a,l);break e}else if(a!==n){n=vt(Error(r(424)),t),Ia(n),t=io(e,t,a,l);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Oe=Et(e.firstChild),We=t,he=!0,Dl=null,Ot=!0,l=Xf(t,null,a,l),t.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(Pa(),a===n){t=Xt(e,t,l);break e}Qe(e,t,a,l)}t=t.child}return t;case 26:return Ou(e,t),e===null?(l=gd(t.type,null,t.pendingProps,null))?t.memoizedState=l:he||(l=t.type,e=t.pendingProps,a=Qu(ae.current).createElement(l),a[Ke]=t,a[Pe]=e,Ve(a,l,e),Ye(a),t.stateNode=a):t.memoizedState=gd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return ni(t),e===null&&he&&(a=t.stateNode=dd(t.type,t.pendingProps,ae.current),We=t,Ot=!0,n=Oe,gl(t.type)?(rc=n,Oe=Et(a.firstChild)):Oe=n),Qe(e,t,t.pendingProps.children,l),Ou(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&he&&((n=a=Oe)&&(a=yg(a,t.type,t.pendingProps,Ot),a!==null?(t.stateNode=a,We=t,Oe=Et(a.firstChild),Ot=!1,n=!0):n=!1),n||Cl(t)),ni(t),n=t.type,u=t.pendingProps,c=e!==null?e.memoizedProps:null,a=u.children,uc(n,u)?a=null:c!==null&&uc(n,c)&&(t.flags|=32),t.memoizedState!==null&&(n=ns(e,t,Hm,null,null,l),zn._currentValue=n),Ou(e,t),Qe(e,t,a,l),t.child;case 6:return e===null&&he&&((e=l=Oe)&&(l=xg(l,t.pendingProps,Ot),l!==null?(t.stateNode=l,We=t,Oe=null,e=!0):e=!1),e||Cl(t)),null;case 13:return so(e,t,l);case 4:return Te(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=Sa(t,null,a,l):Qe(e,t,a,l),t.child;case 11:return If(e,t,t.type,t.pendingProps,l);case 7:return Qe(e,t,t.pendingProps,l),t.child;case 8:return Qe(e,t,t.pendingProps.children,l),t.child;case 12:return Qe(e,t,t.pendingProps.children,l),t.child;case 10:return a=t.pendingProps,el(t,t.type,a.value),Qe(e,t,a.children,l),t.child;case 9:return n=t.type._context,a=t.pendingProps.children,Ul(t),n=Je(n),a=a(n),t.flags|=1,Qe(e,t,a,l),t.child;case 14:return eo(e,t,t.type,t.pendingProps,l);case 15:return to(e,t,t.type,t.pendingProps,l);case 19:return ro(e,t,l);case 31:return a=t.pendingProps,l=t.mode,a={mode:a.mode,children:a.children},e===null?(l=Ru(a,l),l.ref=t.ref,t.child=l,l.return=t,t=l):(l=Bt(e.child,a),l.ref=t.ref,t.child=l,l.return=t,t=l),t;case 22:return lo(e,t,l);case 24:return Ul(t),a=Je(He),e===null?(n=$i(),n===null&&(n=Ne,u=Ji(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=l),n=u),t.memoizedState={parent:a,cache:n},Wi(t),el(t,He,n)):((e.lanes&l)!==0&&(Pi(e,t),cn(t,null,null,l),sn()),n=e.memoizedState,u=t.memoizedState,n.parent!==a?(n={parent:a,cache:a},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),el(t,He,a)):(a=u.cache,el(t,He,a),a!==n.cache&&Ki(t,[He],l,!0))),Qe(e,t,t.pendingProps.children,l),t.child;case 29:throw t.pendingProps}throw Error(r(156,t.tag))}function Qt(e){e.flags|=4}function oo(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!bd(t)){if(t=bt.current,t!==null&&((re&4194048)===re?Rt!==null:(re&62914560)!==re&&(re&536870912)===0||t!==Rt))throw nn=Fi,Jr;e.flags|=8192}}function zu(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Xc():536870912,e.lanes|=t,Ea|=t)}function gn(e,t){if(!he)switch(e.tailMode){case"hidden":t=e.tail;for(var l=null;t!==null;)t.alternate!==null&&(l=t),t=t.sibling;l===null?e.tail=null:l.sibling=null;break;case"collapsed":l=e.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function Me(e){var t=e.alternate!==null&&e.alternate.child===e.child,l=0,a=0;if(t)for(var n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=a,e.childLanes=l,t}function Vm(e,t,l){var a=t.pendingProps;switch(Xi(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Me(t),null;case 1:return Me(t),null;case 3:return l=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Yt(He),Ft(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(Wa(t)?Qt(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Xr())),Me(t),null;case 26:return l=t.memoizedState,e===null?(Qt(t),l!==null?(Me(t),oo(t,l)):(Me(t),t.flags&=-16777217)):l?l!==e.memoizedState?(Qt(t),Me(t),oo(t,l)):(Me(t),t.flags&=-16777217):(e.memoizedProps!==a&&Qt(t),Me(t),t.flags&=-16777217),null;case 27:Ln(t),l=ae.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Qt(t);else{if(!a){if(t.stateNode===null)throw Error(r(166));return Me(t),null}e=P.current,Wa(t)?Gr(t):(e=dd(n,a,l),t.stateNode=e,Qt(t))}return Me(t),null;case 5:if(Ln(t),l=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Qt(t);else{if(!a){if(t.stateNode===null)throw Error(r(166));return Me(t),null}if(e=P.current,Wa(t))Gr(t);else{switch(n=Qu(ae.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}e[Ke]=t,e[Pe]=a;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(Ve(e,l,a),l){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Qt(t)}}return Me(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&Qt(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(r(166));if(e=ae.current,Wa(t)){if(e=t.stateNode,l=t.memoizedProps,a=null,n=We,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}e[Ke]=t,e=!!(e.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||ud(e.nodeValue,l)),e||Cl(t)}else e=Qu(e).createTextNode(a),e[Ke]=t,t.stateNode=e}return Me(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=Wa(t),a!==null&&a.dehydrated!==null){if(e===null){if(!n)throw Error(r(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(r(317));n[Ke]=t}else Pa(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Me(t),n=!1}else n=Xr(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(Lt(t),t):(Lt(t),null)}if(Lt(t),(t.flags&128)!==0)return t.lanes=l,t;if(l=a!==null,e=e!==null&&e.memoizedState!==null,l){a=t.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return l!==e&&l&&(t.child.flags|=8192),zu(t,t.updateQueue),Me(t),null;case 4:return Ft(),e===null&&ec(t.stateNode.containerInfo),Me(t),null;case 10:return Yt(t.type),Me(t),null;case 19:if(J(qe),n=t.memoizedState,n===null)return Me(t),null;if(a=(t.flags&128)!==0,u=n.rendering,u===null)if(a)gn(n,!1);else{if(Re!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(u=Eu(e),u!==null){for(t.flags|=128,gn(n,!1),e=u.updateQueue,t.updateQueue=e,zu(t,e),t.subtreeFlags=0,e=l,l=t.child;l!==null;)wr(l,e),l=l.sibling;return V(qe,qe.current&1|2),t.child}e=e.sibling}n.tail!==null&&Mt()>_u&&(t.flags|=128,a=!0,gn(n,!1),t.lanes=4194304)}else{if(!a)if(e=Eu(u),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,zu(t,e),gn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!he)return Me(t),null}else 2*Mt()-n.renderingStartTime>_u&&l!==536870912&&(t.flags|=128,a=!0,gn(n,!1),t.lanes=4194304);n.isBackwards?(u.sibling=t.child,t.child=u):(e=n.last,e!==null?e.sibling=u:t.child=u,n.last=u)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=Mt(),t.sibling=null,e=qe.current,V(qe,a?e&1|2:e&1),t):(Me(t),null);case 22:case 23:return Lt(t),ls(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(l&536870912)!==0&&(t.flags&128)===0&&(Me(t),t.subtreeFlags&6&&(t.flags|=8192)):Me(t),l=t.updateQueue,l!==null&&zu(t,l.retryQueue),l=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==l&&(t.flags|=2048),e!==null&&J(Bl),null;case 24:return l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Yt(He),Me(t),null;case 25:return null;case 30:return null}throw Error(r(156,t.tag))}function Km(e,t){switch(Xi(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Yt(He),Ft(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Ln(t),null;case 13:if(Lt(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(r(340));Pa()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return J(qe),null;case 4:return Ft(),null;case 10:return Yt(t.type),null;case 22:case 23:return Lt(t),ls(),e!==null&&J(Bl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Yt(He),null;case 25:return null;default:return null}}function ho(e,t){switch(Xi(t),t.tag){case 3:Yt(He),Ft();break;case 26:case 27:case 5:Ln(t);break;case 4:Ft();break;case 13:Lt(t);break;case 19:J(qe);break;case 10:Yt(t.type);break;case 22:case 23:Lt(t),ls(),e!==null&&J(Bl);break;case 24:Yt(He)}}function vn(e,t){try{var l=t.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&e)===e){a=void 0;var u=l.create,c=l.inst;a=u(),c.destroy=a}l=l.next}while(l!==n)}}catch(o){be(t,t.return,o)}}function sl(e,t,l){try{var a=t.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&e)===e){var c=a.inst,o=c.destroy;if(o!==void 0){c.destroy=void 0,n=t;var v=l,A=o;try{A()}catch(B){be(n,v,B)}}}a=a.next}while(a!==u)}}catch(B){be(t,t.return,B)}}function mo(e){var t=e.updateQueue;if(t!==null){var l=e.stateNode;try{Ir(t,l)}catch(a){be(e,e.return,a)}}}function go(e,t,l){l.props=ql(e.type,e.memoizedProps),l.state=e.memoizedState;try{l.componentWillUnmount()}catch(a){be(e,t,a)}}function pn(e,t){try{var l=e.ref;if(l!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof l=="function"?e.refCleanup=l(a):l.current=a}}catch(n){be(e,t,n)}}function zt(e,t){var l=e.ref,a=e.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){be(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){be(e,t,n)}else l.current=null}function vo(e){var t=e.type,l=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break e;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){be(e,e.return,n)}}function zs(e,t,l){try{var a=e.stateNode;hg(a,e.type,l,t),a[Pe]=t}catch(n){be(e,e.return,n)}}function po(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&gl(e.type)||e.tag===4}function Ds(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||po(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&gl(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Cs(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(e,t):(t=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,t.appendChild(e),l=l._reactRootContainer,l!=null||t.onclick!==null||(t.onclick=Xu));else if(a!==4&&(a===27&&gl(e.type)&&(l=e.stateNode,t=null),e=e.child,e!==null))for(Cs(e,t,l),e=e.sibling;e!==null;)Cs(e,t,l),e=e.sibling}function Du(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?l.insertBefore(e,t):l.appendChild(e);else if(a!==4&&(a===27&&gl(e.type)&&(l=e.stateNode),e=e.child,e!==null))for(Du(e,t,l),e=e.sibling;e!==null;)Du(e,t,l),e=e.sibling}function yo(e){var t=e.stateNode,l=e.memoizedProps;try{for(var a=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);Ve(t,a,l),t[Ke]=e,t[Pe]=l}catch(u){be(e,e.return,u)}}var Zt=!1,De=!1,_s=!1,xo=typeof WeakSet=="function"?WeakSet:Set,Le=null;function Jm(e,t){if(e=e.containerInfo,ac=$u,e=Or(e),Di(e)){if("selectionStart"in e)var l={start:e.selectionStart,end:e.selectionEnd};else e:{l=(l=e.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break e}var c=0,o=-1,v=-1,A=0,B=0,q=e,R=null;t:for(;;){for(var D;q!==l||n!==0&&q.nodeType!==3||(o=c+n),q!==u||a!==0&&q.nodeType!==3||(v=c+a),q.nodeType===3&&(c+=q.nodeValue.length),(D=q.firstChild)!==null;)R=q,q=D;for(;;){if(q===e)break t;if(R===l&&++A===n&&(o=c),R===u&&++B===a&&(v=c),(D=q.nextSibling)!==null)break;q=R,R=q.parentNode}q=D}l=o===-1||v===-1?null:{start:o,end:v}}else l=null}l=l||{start:0,end:0}}else l=null;for(nc={focusedElem:e,selectionRange:l},$u=!1,Le=t;Le!==null;)if(t=Le,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Le=e;else for(;Le!==null;){switch(t=Le,u=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&u!==null){e=void 0,l=t,n=u.memoizedProps,u=u.memoizedState,a=l.stateNode;try{var le=ql(l.type,n,l.elementType===l.type);e=a.getSnapshotBeforeUpdate(le,u),a.__reactInternalSnapshotBeforeUpdate=e}catch(I){be(l,l.return,I)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,l=e.nodeType,l===9)sc(e);else if(l===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":sc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(r(163))}if(e=t.sibling,e!==null){e.return=t.return,Le=e;break}Le=t.return}}function bo(e,t,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:cl(e,l),a&4&&vn(5,l);break;case 1:if(cl(e,l),a&4)if(e=l.stateNode,t===null)try{e.componentDidMount()}catch(c){be(l,l.return,c)}else{var n=ql(l.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(c){be(l,l.return,c)}}a&64&&mo(l),a&512&&pn(l,l.return);break;case 3:if(cl(e,l),a&64&&(e=l.updateQueue,e!==null)){if(t=null,l.child!==null)switch(l.child.tag){case 27:case 5:t=l.child.stateNode;break;case 1:t=l.child.stateNode}try{Ir(e,t)}catch(c){be(l,l.return,c)}}break;case 27:t===null&&a&4&&yo(l);case 26:case 5:cl(e,l),t===null&&a&4&&vo(l),a&512&&pn(l,l.return);break;case 12:cl(e,l);break;case 13:cl(e,l),a&4&&No(e,l),a&64&&(e=l.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(l=lg.bind(null,l),bg(e,l))));break;case 22:if(a=l.memoizedState!==null||Zt,!a){t=t!==null&&t.memoizedState!==null||De,n=Zt;var u=De;Zt=a,(De=t)&&!u?rl(e,l,(l.subtreeFlags&8772)!==0):cl(e,l),Zt=n,De=u}break;case 30:break;default:cl(e,l)}}function So(e){var t=e.alternate;t!==null&&(e.alternate=null,So(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&di(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ee=null,tt=!1;function Vt(e,t,l){for(l=l.child;l!==null;)jo(e,t,l),l=l.sibling}function jo(e,t,l){if(nt&&typeof nt.onCommitFiberUnmount=="function")try{nt.onCommitFiberUnmount(qa,l)}catch{}switch(l.tag){case 26:De||zt(l,t),Vt(e,t,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:De||zt(l,t);var a=Ee,n=tt;gl(l.type)&&(Ee=l.stateNode,tt=!1),Vt(e,t,l),An(l.stateNode),Ee=a,tt=n;break;case 5:De||zt(l,t);case 6:if(a=Ee,n=tt,Ee=null,Vt(e,t,l),Ee=a,tt=n,Ee!==null)if(tt)try{(Ee.nodeType===9?Ee.body:Ee.nodeName==="HTML"?Ee.ownerDocument.body:Ee).removeChild(l.stateNode)}catch(u){be(l,t,u)}else try{Ee.removeChild(l.stateNode)}catch(u){be(l,t,u)}break;case 18:Ee!==null&&(tt?(e=Ee,fd(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,l.stateNode),Un(e)):fd(Ee,l.stateNode));break;case 4:a=Ee,n=tt,Ee=l.stateNode.containerInfo,tt=!0,Vt(e,t,l),Ee=a,tt=n;break;case 0:case 11:case 14:case 15:De||sl(2,l,t),De||sl(4,l,t),Vt(e,t,l);break;case 1:De||(zt(l,t),a=l.stateNode,typeof a.componentWillUnmount=="function"&&go(l,t,a)),Vt(e,t,l);break;case 21:Vt(e,t,l);break;case 22:De=(a=De)||l.memoizedState!==null,Vt(e,t,l),De=a;break;default:Vt(e,t,l)}}function No(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Un(e)}catch(l){be(t,t.return,l)}}function km(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new xo),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new xo),t;default:throw Error(r(435,e.tag))}}function Us(e,t){var l=km(e);t.forEach(function(a){var n=ag.bind(null,e,a);l.has(a)||(l.add(a),a.then(n,n))})}function ct(e,t){var l=t.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],u=e,c=t,o=c;e:for(;o!==null;){switch(o.tag){case 27:if(gl(o.type)){Ee=o.stateNode,tt=!1;break e}break;case 5:Ee=o.stateNode,tt=!1;break e;case 3:case 4:Ee=o.stateNode.containerInfo,tt=!0;break e}o=o.return}if(Ee===null)throw Error(r(160));jo(u,c,n),Ee=null,tt=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)To(t,e),t=t.sibling}var Tt=null;function To(e,t){var l=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:ct(t,e),rt(e),a&4&&(sl(3,e,e.return),vn(3,e),sl(5,e,e.return));break;case 1:ct(t,e),rt(e),a&512&&(De||l===null||zt(l,l.return)),a&64&&Zt&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(l=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Tt;if(ct(t,e),rt(e),a&512&&(De||l===null||zt(l,l.return)),a&4){var u=l!==null?l.memoizedState:null;if(a=e.memoizedState,l===null)if(a===null)if(e.stateNode===null){e:{a=e.type,l=e.memoizedProps,n=n.ownerDocument||n;t:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[Ga]||u[Ke]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),Ve(u,a,l),u[Ke]=e,Ye(u),a=u;break e;case"link":var c=yd("link","href",n).get(a+(l.href||""));if(c){for(var o=0;o<c.length;o++)if(u=c[o],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){c.splice(o,1);break t}}u=n.createElement(a),Ve(u,a,l),n.head.appendChild(u);break;case"meta":if(c=yd("meta","content",n).get(a+(l.content||""))){for(o=0;o<c.length;o++)if(u=c[o],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){c.splice(o,1);break t}}u=n.createElement(a),Ve(u,a,l),n.head.appendChild(u);break;default:throw Error(r(468,a))}u[Ke]=e,Ye(u),a=u}e.stateNode=a}else xd(n,e.type,e.stateNode);else e.stateNode=pd(n,a,e.memoizedProps);else u!==a?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,a===null?xd(n,e.type,e.stateNode):pd(n,a,e.memoizedProps)):a===null&&e.stateNode!==null&&zs(e,e.memoizedProps,l.memoizedProps)}break;case 27:ct(t,e),rt(e),a&512&&(De||l===null||zt(l,l.return)),l!==null&&a&4&&zs(e,e.memoizedProps,l.memoizedProps);break;case 5:if(ct(t,e),rt(e),a&512&&(De||l===null||zt(l,l.return)),e.flags&32){n=e.stateNode;try{aa(n,"")}catch(D){be(e,e.return,D)}}a&4&&e.stateNode!=null&&(n=e.memoizedProps,zs(e,n,l!==null?l.memoizedProps:n)),a&1024&&(_s=!0);break;case 6:if(ct(t,e),rt(e),a&4){if(e.stateNode===null)throw Error(r(162));a=e.memoizedProps,l=e.stateNode;try{l.nodeValue=a}catch(D){be(e,e.return,D)}}break;case 3:if(Ku=null,n=Tt,Tt=Zu(t.containerInfo),ct(t,e),Tt=n,rt(e),a&4&&l!==null&&l.memoizedState.isDehydrated)try{Un(t.containerInfo)}catch(D){be(e,e.return,D)}_s&&(_s=!1,Eo(e));break;case 4:a=Tt,Tt=Zu(e.stateNode.containerInfo),ct(t,e),rt(e),Tt=a;break;case 12:ct(t,e),rt(e);break;case 13:ct(t,e),rt(e),e.child.flags&8192&&e.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Gs=Mt()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Us(e,a)));break;case 22:n=e.memoizedState!==null;var v=l!==null&&l.memoizedState!==null,A=Zt,B=De;if(Zt=A||n,De=B||v,ct(t,e),De=B,Zt=A,rt(e),a&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(l===null||v||Zt||De||wl(e)),l=null,t=e;;){if(t.tag===5||t.tag===26){if(l===null){v=l=t;try{if(u=v.stateNode,n)c=u.style,typeof c.setProperty=="function"?c.setProperty("display","none","important"):c.display="none";else{o=v.stateNode;var q=v.memoizedProps.style,R=q!=null&&q.hasOwnProperty("display")?q.display:null;o.style.display=R==null||typeof R=="boolean"?"":(""+R).trim()}}catch(D){be(v,v.return,D)}}}else if(t.tag===6){if(l===null){v=t;try{v.stateNode.nodeValue=n?"":v.memoizedProps}catch(D){be(v,v.return,D)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;l===t&&(l=null),t=t.return}l===t&&(l=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Us(e,l))));break;case 19:ct(t,e),rt(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Us(e,a)));break;case 30:break;case 21:break;default:ct(t,e),rt(e)}}function rt(e){var t=e.flags;if(t&2){try{for(var l,a=e.return;a!==null;){if(po(a)){l=a;break}a=a.return}if(l==null)throw Error(r(160));switch(l.tag){case 27:var n=l.stateNode,u=Ds(e);Du(e,u,n);break;case 5:var c=l.stateNode;l.flags&32&&(aa(c,""),l.flags&=-33);var o=Ds(e);Du(e,o,c);break;case 3:case 4:var v=l.stateNode.containerInfo,A=Ds(e);Cs(e,A,v);break;default:throw Error(r(161))}}catch(B){be(e,e.return,B)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Eo(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Eo(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function cl(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)bo(e,t.alternate,t),t=t.sibling}function wl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:sl(4,t,t.return),wl(t);break;case 1:zt(t,t.return);var l=t.stateNode;typeof l.componentWillUnmount=="function"&&go(t,t.return,l),wl(t);break;case 27:An(t.stateNode);case 26:case 5:zt(t,t.return),wl(t);break;case 22:t.memoizedState===null&&wl(t);break;case 30:wl(t);break;default:wl(t)}e=e.sibling}}function rl(e,t,l){for(l=l&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,n=e,u=t,c=u.flags;switch(u.tag){case 0:case 11:case 15:rl(n,u,l),vn(4,u);break;case 1:if(rl(n,u,l),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(A){be(a,a.return,A)}if(a=u,n=a.updateQueue,n!==null){var o=a.stateNode;try{var v=n.shared.hiddenCallbacks;if(v!==null)for(n.shared.hiddenCallbacks=null,n=0;n<v.length;n++)Pr(v[n],o)}catch(A){be(a,a.return,A)}}l&&c&64&&mo(u),pn(u,u.return);break;case 27:yo(u);case 26:case 5:rl(n,u,l),l&&a===null&&c&4&&vo(u),pn(u,u.return);break;case 12:rl(n,u,l);break;case 13:rl(n,u,l),l&&c&4&&No(n,u);break;case 22:u.memoizedState===null&&rl(n,u,l),pn(u,u.return);break;case 30:break;default:rl(n,u,l)}t=t.sibling}}function Bs(e,t){var l=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==l&&(e!=null&&e.refCount++,l!=null&&tn(l))}function Hs(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&tn(e))}function Dt(e,t,l,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Ao(e,t,l,a),t=t.sibling}function Ao(e,t,l,a){var n=t.flags;switch(t.tag){case 0:case 11:case 15:Dt(e,t,l,a),n&2048&&vn(9,t);break;case 1:Dt(e,t,l,a);break;case 3:Dt(e,t,l,a),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&tn(e)));break;case 12:if(n&2048){Dt(e,t,l,a),e=t.stateNode;try{var u=t.memoizedProps,c=u.id,o=u.onPostCommit;typeof o=="function"&&o(c,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(v){be(t,t.return,v)}}else Dt(e,t,l,a);break;case 13:Dt(e,t,l,a);break;case 23:break;case 22:u=t.stateNode,c=t.alternate,t.memoizedState!==null?u._visibility&2?Dt(e,t,l,a):yn(e,t):u._visibility&2?Dt(e,t,l,a):(u._visibility|=2,ja(e,t,l,a,(t.subtreeFlags&10256)!==0)),n&2048&&Bs(c,t);break;case 24:Dt(e,t,l,a),n&2048&&Hs(t.alternate,t);break;default:Dt(e,t,l,a)}}function ja(e,t,l,a,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var u=e,c=t,o=l,v=a,A=c.flags;switch(c.tag){case 0:case 11:case 15:ja(u,c,o,v,n),vn(8,c);break;case 23:break;case 22:var B=c.stateNode;c.memoizedState!==null?B._visibility&2?ja(u,c,o,v,n):yn(u,c):(B._visibility|=2,ja(u,c,o,v,n)),n&&A&2048&&Bs(c.alternate,c);break;case 24:ja(u,c,o,v,n),n&&A&2048&&Hs(c.alternate,c);break;default:ja(u,c,o,v,n)}t=t.sibling}}function yn(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var l=e,a=t,n=a.flags;switch(a.tag){case 22:yn(l,a),n&2048&&Bs(a.alternate,a);break;case 24:yn(l,a),n&2048&&Hs(a.alternate,a);break;default:yn(l,a)}t=t.sibling}}var xn=8192;function Na(e){if(e.subtreeFlags&xn)for(e=e.child;e!==null;)Mo(e),e=e.sibling}function Mo(e){switch(e.tag){case 26:Na(e),e.flags&xn&&e.memoizedState!==null&&_g(Tt,e.memoizedState,e.memoizedProps);break;case 5:Na(e);break;case 3:case 4:var t=Tt;Tt=Zu(e.stateNode.containerInfo),Na(e),Tt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=xn,xn=16777216,Na(e),xn=t):Na(e));break;default:Na(e)}}function Oo(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function bn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];Le=a,zo(a,e)}Oo(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Ro(e),e=e.sibling}function Ro(e){switch(e.tag){case 0:case 11:case 15:bn(e),e.flags&2048&&sl(9,e,e.return);break;case 3:bn(e);break;case 12:bn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Cu(e)):bn(e);break;default:bn(e)}}function Cu(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];Le=a,zo(a,e)}Oo(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:sl(8,t,t.return),Cu(t);break;case 22:l=t.stateNode,l._visibility&2&&(l._visibility&=-3,Cu(t));break;default:Cu(t)}e=e.sibling}}function zo(e,t){for(;Le!==null;){var l=Le;switch(l.tag){case 0:case 11:case 15:sl(8,l,t);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:tn(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,Le=a;else e:for(l=e;Le!==null;){a=Le;var n=a.sibling,u=a.return;if(So(a),a===l){Le=null;break e}if(n!==null){n.return=u,Le=n;break e}Le=u}}}var $m={getCacheForType:function(e){var t=Je(He),l=t.data.get(e);return l===void 0&&(l=e(),t.data.set(e,l)),l}},Fm=typeof WeakMap=="function"?WeakMap:Map,me=0,Ne=null,se=null,re=0,ge=0,ft=null,fl=!1,Ta=!1,qs=!1,Kt=0,Re=0,ol=0,Yl=0,ws=0,St=0,Ea=0,Sn=null,lt=null,Ys=!1,Gs=0,_u=1/0,Uu=null,dl=null,Ze=0,hl=null,Aa=null,Ma=0,Ls=0,Xs=null,Do=null,jn=0,Qs=null;function ot(){if((me&2)!==0&&re!==0)return re&-re;if(U.T!==null){var e=ma;return e!==0?e:Fs()}return Vc()}function Co(){St===0&&(St=(re&536870912)===0||he?Lc():536870912);var e=bt.current;return e!==null&&(e.flags|=32),St}function dt(e,t,l){(e===Ne&&(ge===2||ge===9)||e.cancelPendingCommit!==null)&&(Oa(e,0),ml(e,re,St,!1)),Ya(e,l),((me&2)===0||e!==Ne)&&(e===Ne&&((me&2)===0&&(Yl|=l),Re===4&&ml(e,re,St,!1)),Ct(e))}function _o(e,t,l){if((me&6)!==0)throw Error(r(327));var a=!l&&(t&124)===0&&(t&e.expiredLanes)===0||wa(e,t),n=a?Im(e,t):Ks(e,t,!0),u=a;do{if(n===0){Ta&&!a&&ml(e,t,0,!1);break}else{if(l=e.current.alternate,u&&!Wm(l)){n=Ks(e,t,!1),u=!1;continue}if(n===2){if(u=t,e.errorRecoveryDisabledLanes&u)var c=0;else c=e.pendingLanes&-536870913,c=c!==0?c:c&536870912?536870912:0;if(c!==0){t=c;e:{var o=e;n=Sn;var v=o.current.memoizedState.isDehydrated;if(v&&(Oa(o,c).flags|=256),c=Ks(o,c,!1),c!==2){if(qs&&!v){o.errorRecoveryDisabledLanes|=u,Yl|=u,n=4;break e}u=lt,lt=n,u!==null&&(lt===null?lt=u:lt.push.apply(lt,u))}n=c}if(u=!1,n!==2)continue}}if(n===1){Oa(e,0),ml(e,t,0,!0);break}e:{switch(a=e,u=n,u){case 0:case 1:throw Error(r(345));case 4:if((t&4194048)!==t)break;case 6:ml(a,t,St,!fl);break e;case 2:lt=null;break;case 3:case 5:break;default:throw Error(r(329))}if((t&62914560)===t&&(n=Gs+300-Mt(),10<n)){if(ml(a,t,St,!fl),Vn(a,0,!0)!==0)break e;a.timeoutHandle=cd(Uo.bind(null,a,l,lt,Uu,Ys,t,St,Yl,Ea,fl,u,2,-0,0),n);break e}Uo(a,l,lt,Uu,Ys,t,St,Yl,Ea,fl,u,0,-0,0)}}break}while(!0);Ct(e)}function Uo(e,t,l,a,n,u,c,o,v,A,B,q,R,D){if(e.timeoutHandle=-1,q=t.subtreeFlags,(q&8192||(q&16785408)===16785408)&&(Rn={stylesheets:null,count:0,unsuspend:Cg},Mo(t),q=Ug(),q!==null)){e.cancelPendingCommit=q(Lo.bind(null,e,t,u,l,a,n,c,o,v,B,1,R,D)),ml(e,u,c,!A);return}Lo(e,t,u,l,a,n,c,o,v)}function Wm(e){for(var t=e;;){var l=t.tag;if((l===0||l===11||l===15)&&t.flags&16384&&(l=t.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],u=n.getSnapshot;n=n.value;try{if(!it(u(),n))return!1}catch{return!1}}if(l=t.child,t.subtreeFlags&16384&&l!==null)l.return=t,t=l;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ml(e,t,l,a){t&=~ws,t&=~Yl,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var n=t;0<n;){var u=31-ut(n),c=1<<u;a[u]=-1,n&=~c}l!==0&&Qc(e,l,t)}function Bu(){return(me&6)===0?(Nn(0),!1):!0}function Zs(){if(se!==null){if(ge===0)var e=se.return;else e=se,wt=_l=null,ss(e),ba=null,hn=0,e=se;for(;e!==null;)ho(e.alternate,e),e=e.return;se=null}}function Oa(e,t){var l=e.timeoutHandle;l!==-1&&(e.timeoutHandle=-1,gg(l)),l=e.cancelPendingCommit,l!==null&&(e.cancelPendingCommit=null,l()),Zs(),Ne=e,se=l=Bt(e.current,null),re=t,ge=0,ft=null,fl=!1,Ta=wa(e,t),qs=!1,Ea=St=ws=Yl=ol=Re=0,lt=Sn=null,Ys=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var n=31-ut(a),u=1<<n;t|=e[n],a&=~u}return Kt=t,au(),l}function Bo(e,t){ue=null,U.H=ju,t===an||t===du?(t=Fr(),ge=3):t===Jr?(t=Fr(),ge=4):ge=t===Pf?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,ft=t,se===null&&(Re=1,Mu(e,vt(t,e.current)))}function Ho(){var e=U.H;return U.H=ju,e===null?ju:e}function qo(){var e=U.A;return U.A=$m,e}function Vs(){Re=4,fl||(re&4194048)!==re&&bt.current!==null||(Ta=!0),(ol&134217727)===0&&(Yl&134217727)===0||Ne===null||ml(Ne,re,St,!1)}function Ks(e,t,l){var a=me;me|=2;var n=Ho(),u=qo();(Ne!==e||re!==t)&&(Uu=null,Oa(e,t)),t=!1;var c=Re;e:do try{if(ge!==0&&se!==null){var o=se,v=ft;switch(ge){case 8:Zs(),c=6;break e;case 3:case 2:case 9:case 6:bt.current===null&&(t=!0);var A=ge;if(ge=0,ft=null,Ra(e,o,v,A),l&&Ta){c=0;break e}break;default:A=ge,ge=0,ft=null,Ra(e,o,v,A)}}Pm(),c=Re;break}catch(B){Bo(e,B)}while(!0);return t&&e.shellSuspendCounter++,wt=_l=null,me=a,U.H=n,U.A=u,se===null&&(Ne=null,re=0,au()),c}function Pm(){for(;se!==null;)wo(se)}function Im(e,t){var l=me;me|=2;var a=Ho(),n=qo();Ne!==e||re!==t?(Uu=null,_u=Mt()+500,Oa(e,t)):Ta=wa(e,t);e:do try{if(ge!==0&&se!==null){t=se;var u=ft;t:switch(ge){case 1:ge=0,ft=null,Ra(e,t,u,1);break;case 2:case 9:if(kr(u)){ge=0,ft=null,Yo(t);break}t=function(){ge!==2&&ge!==9||Ne!==e||(ge=7),Ct(e)},u.then(t,t);break e;case 3:ge=7;break e;case 4:ge=5;break e;case 7:kr(u)?(ge=0,ft=null,Yo(t)):(ge=0,ft=null,Ra(e,t,u,7));break;case 5:var c=null;switch(se.tag){case 26:c=se.memoizedState;case 5:case 27:var o=se;if(!c||bd(c)){ge=0,ft=null;var v=o.sibling;if(v!==null)se=v;else{var A=o.return;A!==null?(se=A,Hu(A)):se=null}break t}}ge=0,ft=null,Ra(e,t,u,5);break;case 6:ge=0,ft=null,Ra(e,t,u,6);break;case 8:Zs(),Re=6;break e;default:throw Error(r(462))}}eg();break}catch(B){Bo(e,B)}while(!0);return wt=_l=null,U.H=a,U.A=n,me=l,se!==null?0:(Ne=null,re=0,au(),Re)}function eg(){for(;se!==null&&!jh();)wo(se)}function wo(e){var t=fo(e.alternate,e,Kt);e.memoizedProps=e.pendingProps,t===null?Hu(e):se=t}function Yo(e){var t=e,l=t.alternate;switch(t.tag){case 15:case 0:t=no(l,t,t.pendingProps,t.type,void 0,re);break;case 11:t=no(l,t,t.pendingProps,t.type.render,t.ref,re);break;case 5:ss(t);default:ho(l,t),t=se=wr(t,Kt),t=fo(l,t,Kt)}e.memoizedProps=e.pendingProps,t===null?Hu(e):se=t}function Ra(e,t,l,a){wt=_l=null,ss(t),ba=null,hn=0;var n=t.return;try{if(Qm(e,n,t,l,re)){Re=1,Mu(e,vt(l,e.current)),se=null;return}}catch(u){if(n!==null)throw se=n,u;Re=1,Mu(e,vt(l,e.current)),se=null;return}t.flags&32768?(he||a===1?e=!0:Ta||(re&536870912)!==0?e=!1:(fl=e=!0,(a===2||a===9||a===3||a===6)&&(a=bt.current,a!==null&&a.tag===13&&(a.flags|=16384))),Go(t,e)):Hu(t)}function Hu(e){var t=e;do{if((t.flags&32768)!==0){Go(t,fl);return}e=t.return;var l=Vm(t.alternate,t,Kt);if(l!==null){se=l;return}if(t=t.sibling,t!==null){se=t;return}se=t=e}while(t!==null);Re===0&&(Re=5)}function Go(e,t){do{var l=Km(e.alternate,e);if(l!==null){l.flags&=32767,se=l;return}if(l=e.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!t&&(e=e.sibling,e!==null)){se=e;return}se=e=l}while(e!==null);Re=6,se=null}function Lo(e,t,l,a,n,u,c,o,v){e.cancelPendingCommit=null;do qu();while(Ze!==0);if((me&6)!==0)throw Error(r(327));if(t!==null){if(t===e.current)throw Error(r(177));if(u=t.lanes|t.childLanes,u|=Hi,Ch(e,l,u,c,o,v),e===Ne&&(se=Ne=null,re=0),Aa=t,hl=e,Ma=l,Ls=u,Xs=n,Do=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,ng(Xn,function(){return Ko(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=U.T,U.T=null,n=Z.p,Z.p=2,c=me,me|=4;try{Jm(e,t,l)}finally{me=c,Z.p=n,U.T=a}}Ze=1,Xo(),Qo(),Zo()}}function Xo(){if(Ze===1){Ze=0;var e=hl,t=Aa,l=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||l){l=U.T,U.T=null;var a=Z.p;Z.p=2;var n=me;me|=4;try{To(t,e);var u=nc,c=Or(e.containerInfo),o=u.focusedElem,v=u.selectionRange;if(c!==o&&o&&o.ownerDocument&&Mr(o.ownerDocument.documentElement,o)){if(v!==null&&Di(o)){var A=v.start,B=v.end;if(B===void 0&&(B=A),"selectionStart"in o)o.selectionStart=A,o.selectionEnd=Math.min(B,o.value.length);else{var q=o.ownerDocument||document,R=q&&q.defaultView||window;if(R.getSelection){var D=R.getSelection(),le=o.textContent.length,I=Math.min(v.start,le),ye=v.end===void 0?I:Math.min(v.end,le);!D.extend&&I>ye&&(c=ye,ye=I,I=c);var j=Ar(o,I),b=Ar(o,ye);if(j&&b&&(D.rangeCount!==1||D.anchorNode!==j.node||D.anchorOffset!==j.offset||D.focusNode!==b.node||D.focusOffset!==b.offset)){var E=q.createRange();E.setStart(j.node,j.offset),D.removeAllRanges(),I>ye?(D.addRange(E),D.extend(b.node,b.offset)):(E.setEnd(b.node,b.offset),D.addRange(E))}}}}for(q=[],D=o;D=D.parentNode;)D.nodeType===1&&q.push({element:D,left:D.scrollLeft,top:D.scrollTop});for(typeof o.focus=="function"&&o.focus(),o=0;o<q.length;o++){var H=q[o];H.element.scrollLeft=H.left,H.element.scrollTop=H.top}}$u=!!ac,nc=ac=null}finally{me=n,Z.p=a,U.T=l}}e.current=t,Ze=2}}function Qo(){if(Ze===2){Ze=0;var e=hl,t=Aa,l=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||l){l=U.T,U.T=null;var a=Z.p;Z.p=2;var n=me;me|=4;try{bo(e,t.alternate,t)}finally{me=n,Z.p=a,U.T=l}}Ze=3}}function Zo(){if(Ze===4||Ze===3){Ze=0,Nh();var e=hl,t=Aa,l=Ma,a=Do;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Ze=5:(Ze=0,Aa=hl=null,Vo(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(dl=null),fi(l),t=t.stateNode,nt&&typeof nt.onCommitFiberRoot=="function")try{nt.onCommitFiberRoot(qa,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=U.T,n=Z.p,Z.p=2,U.T=null;try{for(var u=e.onRecoverableError,c=0;c<a.length;c++){var o=a[c];u(o.value,{componentStack:o.stack})}}finally{U.T=t,Z.p=n}}(Ma&3)!==0&&qu(),Ct(e),n=e.pendingLanes,(l&4194090)!==0&&(n&42)!==0?e===Qs?jn++:(jn=0,Qs=e):jn=0,Nn(0)}}function Vo(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,tn(t)))}function qu(e){return Xo(),Qo(),Zo(),Ko()}function Ko(){if(Ze!==5)return!1;var e=hl,t=Ls;Ls=0;var l=fi(Ma),a=U.T,n=Z.p;try{Z.p=32>l?32:l,U.T=null,l=Xs,Xs=null;var u=hl,c=Ma;if(Ze=0,Aa=hl=null,Ma=0,(me&6)!==0)throw Error(r(331));var o=me;if(me|=4,Ro(u.current),Ao(u,u.current,c,l),me=o,Nn(0,!1),nt&&typeof nt.onPostCommitFiberRoot=="function")try{nt.onPostCommitFiberRoot(qa,u)}catch{}return!0}finally{Z.p=n,U.T=a,Vo(e,t)}}function Jo(e,t,l){t=vt(l,t),t=Ss(e.stateNode,t,2),e=al(e,t,2),e!==null&&(Ya(e,2),Ct(e))}function be(e,t,l){if(e.tag===3)Jo(e,e,l);else for(;t!==null;){if(t.tag===3){Jo(t,e,l);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(dl===null||!dl.has(a))){e=vt(l,e),l=Ff(2),a=al(t,l,2),a!==null&&(Wf(l,a,t,e),Ya(a,2),Ct(a));break}}t=t.return}}function Js(e,t,l){var a=e.pingCache;if(a===null){a=e.pingCache=new Fm;var n=new Set;a.set(t,n)}else n=a.get(t),n===void 0&&(n=new Set,a.set(t,n));n.has(l)||(qs=!0,n.add(l),e=tg.bind(null,e,t,l),t.then(e,e))}function tg(e,t,l){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&l,e.warmLanes&=~l,Ne===e&&(re&l)===l&&(Re===4||Re===3&&(re&62914560)===re&&300>Mt()-Gs?(me&2)===0&&Oa(e,0):ws|=l,Ea===re&&(Ea=0)),Ct(e)}function ko(e,t){t===0&&(t=Xc()),e=fa(e,t),e!==null&&(Ya(e,t),Ct(e))}function lg(e){var t=e.memoizedState,l=0;t!==null&&(l=t.retryLane),ko(e,l)}function ag(e,t){var l=0;switch(e.tag){case 13:var a=e.stateNode,n=e.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(r(314))}a!==null&&a.delete(t),ko(e,l)}function ng(e,t){return ii(e,t)}var wu=null,za=null,ks=!1,Yu=!1,$s=!1,Gl=0;function Ct(e){e!==za&&e.next===null&&(za===null?wu=za=e:za=za.next=e),Yu=!0,ks||(ks=!0,ig())}function Nn(e,t){if(!$s&&Yu){$s=!0;do for(var l=!1,a=wu;a!==null;){if(e!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var c=a.suspendedLanes,o=a.pingedLanes;u=(1<<31-ut(42|e)+1)-1,u&=n&~(c&~o),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,Po(a,u))}else u=re,u=Vn(a,a===Ne?u:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(u&3)===0||wa(a,u)||(l=!0,Po(a,u));a=a.next}while(l);$s=!1}}function ug(){$o()}function $o(){Yu=ks=!1;var e=0;Gl!==0&&(mg()&&(e=Gl),Gl=0);for(var t=Mt(),l=null,a=wu;a!==null;){var n=a.next,u=Fo(a,t);u===0?(a.next=null,l===null?wu=n:l.next=n,n===null&&(za=l)):(l=a,(e!==0||(u&3)!==0)&&(Yu=!0)),a=n}Nn(e)}function Fo(e,t){for(var l=e.suspendedLanes,a=e.pingedLanes,n=e.expirationTimes,u=e.pendingLanes&-62914561;0<u;){var c=31-ut(u),o=1<<c,v=n[c];v===-1?((o&l)===0||(o&a)!==0)&&(n[c]=Dh(o,t)):v<=t&&(e.expiredLanes|=o),u&=~o}if(t=Ne,l=re,l=Vn(e,e===t?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,l===0||e===t&&(ge===2||ge===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&si(a),e.callbackNode=null,e.callbackPriority=0;if((l&3)===0||wa(e,l)){if(t=l&-l,t===e.callbackPriority)return t;switch(a!==null&&si(a),fi(l)){case 2:case 8:l=Yc;break;case 32:l=Xn;break;case 268435456:l=Gc;break;default:l=Xn}return a=Wo.bind(null,e),l=ii(l,a),e.callbackPriority=t,e.callbackNode=l,t}return a!==null&&a!==null&&si(a),e.callbackPriority=2,e.callbackNode=null,2}function Wo(e,t){if(Ze!==0&&Ze!==5)return e.callbackNode=null,e.callbackPriority=0,null;var l=e.callbackNode;if(qu()&&e.callbackNode!==l)return null;var a=re;return a=Vn(e,e===Ne?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(_o(e,a,t),Fo(e,Mt()),e.callbackNode!=null&&e.callbackNode===l?Wo.bind(null,e):null)}function Po(e,t){if(qu())return null;_o(e,t,!0)}function ig(){vg(function(){(me&6)!==0?ii(wc,ug):$o()})}function Fs(){return Gl===0&&(Gl=Lc()),Gl}function Io(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Fn(""+e)}function ed(e,t){var l=t.ownerDocument.createElement("input");return l.name=t.name,l.value=t.value,e.id&&l.setAttribute("form",e.id),t.parentNode.insertBefore(l,t),e=new FormData(e),l.parentNode.removeChild(l),e}function sg(e,t,l,a,n){if(t==="submit"&&l&&l.stateNode===n){var u=Io((n[Pe]||null).action),c=a.submitter;c&&(t=(t=c[Pe]||null)?Io(t.formAction):c.getAttribute("formAction"),t!==null&&(u=t,c=null));var o=new eu("action","action",null,a,n);e.push({event:o,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Gl!==0){var v=c?ed(n,c):new FormData(n);vs(l,{pending:!0,data:v,method:n.method,action:u},null,v)}}else typeof u=="function"&&(o.preventDefault(),v=c?ed(n,c):new FormData(n),vs(l,{pending:!0,data:v,method:n.method,action:u},u,v))},currentTarget:n}]})}}for(var Ws=0;Ws<Bi.length;Ws++){var Ps=Bi[Ws],cg=Ps.toLowerCase(),rg=Ps[0].toUpperCase()+Ps.slice(1);Nt(cg,"on"+rg)}Nt(Dr,"onAnimationEnd"),Nt(Cr,"onAnimationIteration"),Nt(_r,"onAnimationStart"),Nt("dblclick","onDoubleClick"),Nt("focusin","onFocus"),Nt("focusout","onBlur"),Nt(Am,"onTransitionRun"),Nt(Mm,"onTransitionStart"),Nt(Om,"onTransitionCancel"),Nt(Ur,"onTransitionEnd"),ea("onMouseEnter",["mouseout","mouseover"]),ea("onMouseLeave",["mouseout","mouseover"]),ea("onPointerEnter",["pointerout","pointerover"]),ea("onPointerLeave",["pointerout","pointerover"]),Tl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Tl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Tl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Tl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Tl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Tl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Tn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),fg=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Tn));function td(e,t){t=(t&4)!==0;for(var l=0;l<e.length;l++){var a=e[l],n=a.event;a=a.listeners;e:{var u=void 0;if(t)for(var c=a.length-1;0<=c;c--){var o=a[c],v=o.instance,A=o.currentTarget;if(o=o.listener,v!==u&&n.isPropagationStopped())break e;u=o,n.currentTarget=A;try{u(n)}catch(B){Au(B)}n.currentTarget=null,u=v}else for(c=0;c<a.length;c++){if(o=a[c],v=o.instance,A=o.currentTarget,o=o.listener,v!==u&&n.isPropagationStopped())break e;u=o,n.currentTarget=A;try{u(n)}catch(B){Au(B)}n.currentTarget=null,u=v}}}}function ce(e,t){var l=t[oi];l===void 0&&(l=t[oi]=new Set);var a=e+"__bubble";l.has(a)||(ld(t,e,2,!1),l.add(a))}function Is(e,t,l){var a=0;t&&(a|=4),ld(l,e,a,t)}var Gu="_reactListening"+Math.random().toString(36).slice(2);function ec(e){if(!e[Gu]){e[Gu]=!0,Jc.forEach(function(l){l!=="selectionchange"&&(fg.has(l)||Is(l,!1,e),Is(l,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Gu]||(t[Gu]=!0,Is("selectionchange",!1,t))}}function ld(e,t,l,a){switch(Ad(t)){case 2:var n=qg;break;case 8:n=wg;break;default:n=mc}l=n.bind(null,t,l,e),n=void 0,!ji||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),a?n!==void 0?e.addEventListener(t,l,{capture:!0,passive:n}):e.addEventListener(t,l,!0):n!==void 0?e.addEventListener(t,l,{passive:n}):e.addEventListener(t,l,!1)}function tc(e,t,l,a,n){var u=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var c=a.tag;if(c===3||c===4){var o=a.stateNode.containerInfo;if(o===n)break;if(c===4)for(c=a.return;c!==null;){var v=c.tag;if((v===3||v===4)&&c.stateNode.containerInfo===n)return;c=c.return}for(;o!==null;){if(c=Wl(o),c===null)return;if(v=c.tag,v===5||v===6||v===26||v===27){a=u=c;continue e}o=o.parentNode}}a=a.return}sr(function(){var A=u,B=bi(l),q=[];e:{var R=Br.get(e);if(R!==void 0){var D=eu,le=e;switch(e){case"keypress":if(Pn(l)===0)break e;case"keydown":case"keyup":D=nm;break;case"focusin":le="focus",D=Ai;break;case"focusout":le="blur",D=Ai;break;case"beforeblur":case"afterblur":D=Ai;break;case"click":if(l.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":D=fr;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":D=Kh;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":D=sm;break;case Dr:case Cr:case _r:D=$h;break;case Ur:D=rm;break;case"scroll":case"scrollend":D=Zh;break;case"wheel":D=om;break;case"copy":case"cut":case"paste":D=Wh;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":D=dr;break;case"toggle":case"beforetoggle":D=hm}var I=(t&4)!==0,ye=!I&&(e==="scroll"||e==="scrollend"),j=I?R!==null?R+"Capture":null:R;I=[];for(var b=A,E;b!==null;){var H=b;if(E=H.stateNode,H=H.tag,H!==5&&H!==26&&H!==27||E===null||j===null||(H=Xa(b,j),H!=null&&I.push(En(b,H,E))),ye)break;b=b.return}0<I.length&&(R=new D(R,le,null,l,B),q.push({event:R,listeners:I}))}}if((t&7)===0){e:{if(R=e==="mouseover"||e==="pointerover",D=e==="mouseout"||e==="pointerout",R&&l!==xi&&(le=l.relatedTarget||l.fromElement)&&(Wl(le)||le[Fl]))break e;if((D||R)&&(R=B.window===B?B:(R=B.ownerDocument)?R.defaultView||R.parentWindow:window,D?(le=l.relatedTarget||l.toElement,D=A,le=le?Wl(le):null,le!==null&&(ye=p(le),I=le.tag,le!==ye||I!==5&&I!==27&&I!==6)&&(le=null)):(D=null,le=A),D!==le)){if(I=fr,H="onMouseLeave",j="onMouseEnter",b="mouse",(e==="pointerout"||e==="pointerover")&&(I=dr,H="onPointerLeave",j="onPointerEnter",b="pointer"),ye=D==null?R:La(D),E=le==null?R:La(le),R=new I(H,b+"leave",D,l,B),R.target=ye,R.relatedTarget=E,H=null,Wl(B)===A&&(I=new I(j,b+"enter",le,l,B),I.target=E,I.relatedTarget=ye,H=I),ye=H,D&&le)t:{for(I=D,j=le,b=0,E=I;E;E=Da(E))b++;for(E=0,H=j;H;H=Da(H))E++;for(;0<b-E;)I=Da(I),b--;for(;0<E-b;)j=Da(j),E--;for(;b--;){if(I===j||j!==null&&I===j.alternate)break t;I=Da(I),j=Da(j)}I=null}else I=null;D!==null&&ad(q,R,D,I,!1),le!==null&&ye!==null&&ad(q,ye,le,I,!0)}}e:{if(R=A?La(A):window,D=R.nodeName&&R.nodeName.toLowerCase(),D==="select"||D==="input"&&R.type==="file")var k=br;else if(yr(R))if(Sr)k=Nm;else{k=Sm;var ie=bm}else D=R.nodeName,!D||D.toLowerCase()!=="input"||R.type!=="checkbox"&&R.type!=="radio"?A&&yi(A.elementType)&&(k=br):k=jm;if(k&&(k=k(e,A))){xr(q,k,l,B);break e}ie&&ie(e,R,A),e==="focusout"&&A&&R.type==="number"&&A.memoizedProps.value!=null&&pi(R,"number",R.value)}switch(ie=A?La(A):window,e){case"focusin":(yr(ie)||ie.contentEditable==="true")&&(sa=ie,Ci=A,Fa=null);break;case"focusout":Fa=Ci=sa=null;break;case"mousedown":_i=!0;break;case"contextmenu":case"mouseup":case"dragend":_i=!1,Rr(q,l,B);break;case"selectionchange":if(Em)break;case"keydown":case"keyup":Rr(q,l,B)}var $;if(Oi)e:{switch(e){case"compositionstart":var ee="onCompositionStart";break e;case"compositionend":ee="onCompositionEnd";break e;case"compositionupdate":ee="onCompositionUpdate";break e}ee=void 0}else ia?vr(e,l)&&(ee="onCompositionEnd"):e==="keydown"&&l.keyCode===229&&(ee="onCompositionStart");ee&&(hr&&l.locale!=="ko"&&(ia||ee!=="onCompositionStart"?ee==="onCompositionEnd"&&ia&&($=cr()):(It=B,Ni="value"in It?It.value:It.textContent,ia=!0)),ie=Lu(A,ee),0<ie.length&&(ee=new or(ee,e,null,l,B),q.push({event:ee,listeners:ie}),$?ee.data=$:($=pr(l),$!==null&&(ee.data=$)))),($=gm?vm(e,l):pm(e,l))&&(ee=Lu(A,"onBeforeInput"),0<ee.length&&(ie=new or("onBeforeInput","beforeinput",null,l,B),q.push({event:ie,listeners:ee}),ie.data=$)),sg(q,e,A,l,B)}td(q,t)})}function En(e,t,l){return{instance:e,listener:t,currentTarget:l}}function Lu(e,t){for(var l=t+"Capture",a=[];e!==null;){var n=e,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=Xa(e,l),n!=null&&a.unshift(En(e,n,u)),n=Xa(e,t),n!=null&&a.push(En(e,n,u))),e.tag===3)return a;e=e.return}return[]}function Da(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function ad(e,t,l,a,n){for(var u=t._reactName,c=[];l!==null&&l!==a;){var o=l,v=o.alternate,A=o.stateNode;if(o=o.tag,v!==null&&v===a)break;o!==5&&o!==26&&o!==27||A===null||(v=A,n?(A=Xa(l,u),A!=null&&c.unshift(En(l,A,v))):n||(A=Xa(l,u),A!=null&&c.push(En(l,A,v)))),l=l.return}c.length!==0&&e.push({event:t,listeners:c})}var og=/\r\n?/g,dg=/\u0000|\uFFFD/g;function nd(e){return(typeof e=="string"?e:""+e).replace(og,`
`).replace(dg,"")}function ud(e,t){return t=nd(t),nd(e)===t}function Xu(){}function pe(e,t,l,a,n,u){switch(l){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||aa(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&aa(e,""+a);break;case"className":Jn(e,"class",a);break;case"tabIndex":Jn(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Jn(e,l,a);break;case"style":ur(e,a,u);break;case"data":if(t!=="object"){Jn(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||l!=="href")){e.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=Fn(""+a),e.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(t!=="input"&&pe(e,t,"name",n.name,n,null),pe(e,t,"formEncType",n.formEncType,n,null),pe(e,t,"formMethod",n.formMethod,n,null),pe(e,t,"formTarget",n.formTarget,n,null)):(pe(e,t,"encType",n.encType,n,null),pe(e,t,"method",n.method,n,null),pe(e,t,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=Fn(""+a),e.setAttribute(l,a);break;case"onClick":a!=null&&(e.onclick=Xu);break;case"onScroll":a!=null&&ce("scroll",e);break;case"onScrollEnd":a!=null&&ce("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(r(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(r(60));e.innerHTML=l}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}l=Fn(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""+a):e.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""):e.removeAttribute(l);break;case"capture":case"download":a===!0?e.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,a):e.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(l,a):e.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(l):e.setAttribute(l,a);break;case"popover":ce("beforetoggle",e),ce("toggle",e),Kn(e,"popover",a);break;case"xlinkActuate":_t(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":_t(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":_t(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":_t(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":_t(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":_t(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":_t(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":_t(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":_t(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Kn(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=Xh.get(l)||l,Kn(e,l,a))}}function lc(e,t,l,a,n,u){switch(l){case"style":ur(e,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(r(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(r(60));e.innerHTML=l}}break;case"children":typeof a=="string"?aa(e,a):(typeof a=="number"||typeof a=="bigint")&&aa(e,""+a);break;case"onScroll":a!=null&&ce("scroll",e);break;case"onScrollEnd":a!=null&&ce("scrollend",e);break;case"onClick":a!=null&&(e.onclick=Xu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!kc.hasOwnProperty(l))e:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),t=l.slice(2,n?l.length-7:void 0),u=e[Pe]||null,u=u!=null?u[l]:null,typeof u=="function"&&e.removeEventListener(t,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(l in e?e[l]=null:e.hasAttribute(l)&&e.removeAttribute(l)),e.addEventListener(t,a,n);break e}l in e?e[l]=a:a===!0?e.setAttribute(l,""):Kn(e,l,a)}}}function Ve(e,t,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ce("error",e),ce("load",e);var a=!1,n=!1,u;for(u in l)if(l.hasOwnProperty(u)){var c=l[u];if(c!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:pe(e,t,u,c,l,null)}}n&&pe(e,t,"srcSet",l.srcSet,l,null),a&&pe(e,t,"src",l.src,l,null);return;case"input":ce("invalid",e);var o=u=c=n=null,v=null,A=null;for(a in l)if(l.hasOwnProperty(a)){var B=l[a];if(B!=null)switch(a){case"name":n=B;break;case"type":c=B;break;case"checked":v=B;break;case"defaultChecked":A=B;break;case"value":u=B;break;case"defaultValue":o=B;break;case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(r(137,t));break;default:pe(e,t,a,B,l,null)}}tr(e,u,o,v,A,c,n,!1),kn(e);return;case"select":ce("invalid",e),a=c=u=null;for(n in l)if(l.hasOwnProperty(n)&&(o=l[n],o!=null))switch(n){case"value":u=o;break;case"defaultValue":c=o;break;case"multiple":a=o;default:pe(e,t,n,o,l,null)}t=u,l=c,e.multiple=!!a,t!=null?la(e,!!a,t,!1):l!=null&&la(e,!!a,l,!0);return;case"textarea":ce("invalid",e),u=n=a=null;for(c in l)if(l.hasOwnProperty(c)&&(o=l[c],o!=null))switch(c){case"value":a=o;break;case"defaultValue":n=o;break;case"children":u=o;break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(r(91));break;default:pe(e,t,c,o,l,null)}ar(e,a,n,u),kn(e);return;case"option":for(v in l)if(l.hasOwnProperty(v)&&(a=l[v],a!=null))switch(v){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:pe(e,t,v,a,l,null)}return;case"dialog":ce("beforetoggle",e),ce("toggle",e),ce("cancel",e),ce("close",e);break;case"iframe":case"object":ce("load",e);break;case"video":case"audio":for(a=0;a<Tn.length;a++)ce(Tn[a],e);break;case"image":ce("error",e),ce("load",e);break;case"details":ce("toggle",e);break;case"embed":case"source":case"link":ce("error",e),ce("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(A in l)if(l.hasOwnProperty(A)&&(a=l[A],a!=null))switch(A){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:pe(e,t,A,a,l,null)}return;default:if(yi(t)){for(B in l)l.hasOwnProperty(B)&&(a=l[B],a!==void 0&&lc(e,t,B,a,l,void 0));return}}for(o in l)l.hasOwnProperty(o)&&(a=l[o],a!=null&&pe(e,t,o,a,l,null))}function hg(e,t,l,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,c=null,o=null,v=null,A=null,B=null;for(D in l){var q=l[D];if(l.hasOwnProperty(D)&&q!=null)switch(D){case"checked":break;case"value":break;case"defaultValue":v=q;default:a.hasOwnProperty(D)||pe(e,t,D,null,a,q)}}for(var R in a){var D=a[R];if(q=l[R],a.hasOwnProperty(R)&&(D!=null||q!=null))switch(R){case"type":u=D;break;case"name":n=D;break;case"checked":A=D;break;case"defaultChecked":B=D;break;case"value":c=D;break;case"defaultValue":o=D;break;case"children":case"dangerouslySetInnerHTML":if(D!=null)throw Error(r(137,t));break;default:D!==q&&pe(e,t,R,D,a,q)}}vi(e,c,o,v,A,B,u,n);return;case"select":D=c=o=R=null;for(u in l)if(v=l[u],l.hasOwnProperty(u)&&v!=null)switch(u){case"value":break;case"multiple":D=v;default:a.hasOwnProperty(u)||pe(e,t,u,null,a,v)}for(n in a)if(u=a[n],v=l[n],a.hasOwnProperty(n)&&(u!=null||v!=null))switch(n){case"value":R=u;break;case"defaultValue":o=u;break;case"multiple":c=u;default:u!==v&&pe(e,t,n,u,a,v)}t=o,l=c,a=D,R!=null?la(e,!!l,R,!1):!!a!=!!l&&(t!=null?la(e,!!l,t,!0):la(e,!!l,l?[]:"",!1));return;case"textarea":D=R=null;for(o in l)if(n=l[o],l.hasOwnProperty(o)&&n!=null&&!a.hasOwnProperty(o))switch(o){case"value":break;case"children":break;default:pe(e,t,o,null,a,n)}for(c in a)if(n=a[c],u=l[c],a.hasOwnProperty(c)&&(n!=null||u!=null))switch(c){case"value":R=n;break;case"defaultValue":D=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(r(91));break;default:n!==u&&pe(e,t,c,n,a,u)}lr(e,R,D);return;case"option":for(var le in l)if(R=l[le],l.hasOwnProperty(le)&&R!=null&&!a.hasOwnProperty(le))switch(le){case"selected":e.selected=!1;break;default:pe(e,t,le,null,a,R)}for(v in a)if(R=a[v],D=l[v],a.hasOwnProperty(v)&&R!==D&&(R!=null||D!=null))switch(v){case"selected":e.selected=R&&typeof R!="function"&&typeof R!="symbol";break;default:pe(e,t,v,R,a,D)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var I in l)R=l[I],l.hasOwnProperty(I)&&R!=null&&!a.hasOwnProperty(I)&&pe(e,t,I,null,a,R);for(A in a)if(R=a[A],D=l[A],a.hasOwnProperty(A)&&R!==D&&(R!=null||D!=null))switch(A){case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(r(137,t));break;default:pe(e,t,A,R,a,D)}return;default:if(yi(t)){for(var ye in l)R=l[ye],l.hasOwnProperty(ye)&&R!==void 0&&!a.hasOwnProperty(ye)&&lc(e,t,ye,void 0,a,R);for(B in a)R=a[B],D=l[B],!a.hasOwnProperty(B)||R===D||R===void 0&&D===void 0||lc(e,t,B,R,a,D);return}}for(var j in l)R=l[j],l.hasOwnProperty(j)&&R!=null&&!a.hasOwnProperty(j)&&pe(e,t,j,null,a,R);for(q in a)R=a[q],D=l[q],!a.hasOwnProperty(q)||R===D||R==null&&D==null||pe(e,t,q,R,a,D)}var ac=null,nc=null;function Qu(e){return e.nodeType===9?e:e.ownerDocument}function id(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function sd(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function uc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ic=null;function mg(){var e=window.event;return e&&e.type==="popstate"?e===ic?!1:(ic=e,!0):(ic=null,!1)}var cd=typeof setTimeout=="function"?setTimeout:void 0,gg=typeof clearTimeout=="function"?clearTimeout:void 0,rd=typeof Promise=="function"?Promise:void 0,vg=typeof queueMicrotask=="function"?queueMicrotask:typeof rd<"u"?function(e){return rd.resolve(null).then(e).catch(pg)}:cd;function pg(e){setTimeout(function(){throw e})}function gl(e){return e==="head"}function fd(e,t){var l=t,a=0,n=0;do{var u=l.nextSibling;if(e.removeChild(l),u&&u.nodeType===8)if(l=u.data,l==="/$"){if(0<a&&8>a){l=a;var c=e.ownerDocument;if(l&1&&An(c.documentElement),l&2&&An(c.body),l&4)for(l=c.head,An(l),c=l.firstChild;c;){var o=c.nextSibling,v=c.nodeName;c[Ga]||v==="SCRIPT"||v==="STYLE"||v==="LINK"&&c.rel.toLowerCase()==="stylesheet"||l.removeChild(c),c=o}}if(n===0){e.removeChild(u),Un(t);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=u}while(l);Un(t)}function sc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var l=t;switch(t=t.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":sc(l),di(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}e.removeChild(l)}}function yg(e,t,l,a){for(;e.nodeType===1;){var n=l;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[Ga])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(u=e.getAttribute("rel"),u==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(u!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(u=e.getAttribute("src"),(u!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===u)return e}else return e;if(e=Et(e.nextSibling),e===null)break}return null}function xg(e,t,l){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!l||(e=Et(e.nextSibling),e===null))return null;return e}function cc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function bg(e,t){var l=e.ownerDocument;if(e.data!=="$?"||l.readyState==="complete")t();else{var a=function(){t(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function Et(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var rc=null;function od(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var l=e.data;if(l==="$"||l==="$!"||l==="$?"){if(t===0)return e;t--}else l==="/$"&&t++}e=e.previousSibling}return null}function dd(e,t,l){switch(t=Qu(l),e){case"html":if(e=t.documentElement,!e)throw Error(r(452));return e;case"head":if(e=t.head,!e)throw Error(r(453));return e;case"body":if(e=t.body,!e)throw Error(r(454));return e;default:throw Error(r(451))}}function An(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);di(e)}var jt=new Map,hd=new Set;function Zu(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Jt=Z.d;Z.d={f:Sg,r:jg,D:Ng,C:Tg,L:Eg,m:Ag,X:Og,S:Mg,M:Rg};function Sg(){var e=Jt.f(),t=Bu();return e||t}function jg(e){var t=Pl(e);t!==null&&t.tag===5&&t.type==="form"?Cf(t):Jt.r(e)}var Ca=typeof document>"u"?null:document;function md(e,t,l){var a=Ca;if(a&&typeof t=="string"&&t){var n=gt(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),hd.has(n)||(hd.add(n),e={rel:e,crossOrigin:l,href:t},a.querySelector(n)===null&&(t=a.createElement("link"),Ve(t,"link",e),Ye(t),a.head.appendChild(t)))}}function Ng(e){Jt.D(e),md("dns-prefetch",e,null)}function Tg(e,t){Jt.C(e,t),md("preconnect",e,t)}function Eg(e,t,l){Jt.L(e,t,l);var a=Ca;if(a&&e&&t){var n='link[rel="preload"][as="'+gt(t)+'"]';t==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+gt(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+gt(l.imageSizes)+'"]')):n+='[href="'+gt(e)+'"]';var u=n;switch(t){case"style":u=_a(e);break;case"script":u=Ua(e)}jt.has(u)||(e=T({rel:"preload",href:t==="image"&&l&&l.imageSrcSet?void 0:e,as:t},l),jt.set(u,e),a.querySelector(n)!==null||t==="style"&&a.querySelector(Mn(u))||t==="script"&&a.querySelector(On(u))||(t=a.createElement("link"),Ve(t,"link",e),Ye(t),a.head.appendChild(t)))}}function Ag(e,t){Jt.m(e,t);var l=Ca;if(l&&e){var a=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+gt(a)+'"][href="'+gt(e)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Ua(e)}if(!jt.has(u)&&(e=T({rel:"modulepreload",href:e},t),jt.set(u,e),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(On(u)))return}a=l.createElement("link"),Ve(a,"link",e),Ye(a),l.head.appendChild(a)}}}function Mg(e,t,l){Jt.S(e,t,l);var a=Ca;if(a&&e){var n=Il(a).hoistableStyles,u=_a(e);t=t||"default";var c=n.get(u);if(!c){var o={loading:0,preload:null};if(c=a.querySelector(Mn(u)))o.loading=5;else{e=T({rel:"stylesheet",href:e,"data-precedence":t},l),(l=jt.get(u))&&fc(e,l);var v=c=a.createElement("link");Ye(v),Ve(v,"link",e),v._p=new Promise(function(A,B){v.onload=A,v.onerror=B}),v.addEventListener("load",function(){o.loading|=1}),v.addEventListener("error",function(){o.loading|=2}),o.loading|=4,Vu(c,t,a)}c={type:"stylesheet",instance:c,count:1,state:o},n.set(u,c)}}}function Og(e,t){Jt.X(e,t);var l=Ca;if(l&&e){var a=Il(l).hoistableScripts,n=Ua(e),u=a.get(n);u||(u=l.querySelector(On(n)),u||(e=T({src:e,async:!0},t),(t=jt.get(n))&&oc(e,t),u=l.createElement("script"),Ye(u),Ve(u,"link",e),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Rg(e,t){Jt.M(e,t);var l=Ca;if(l&&e){var a=Il(l).hoistableScripts,n=Ua(e),u=a.get(n);u||(u=l.querySelector(On(n)),u||(e=T({src:e,async:!0,type:"module"},t),(t=jt.get(n))&&oc(e,t),u=l.createElement("script"),Ye(u),Ve(u,"link",e),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function gd(e,t,l,a){var n=(n=ae.current)?Zu(n):null;if(!n)throw Error(r(446));switch(e){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(t=_a(l.href),l=Il(n).hoistableStyles,a=l.get(t),a||(a={type:"style",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){e=_a(l.href);var u=Il(n).hoistableStyles,c=u.get(e);if(c||(n=n.ownerDocument||n,c={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,c),(u=n.querySelector(Mn(e)))&&!u._p&&(c.instance=u,c.state.loading=5),jt.has(e)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},jt.set(e,l),u||zg(n,e,l,c.state))),t&&a===null)throw Error(r(528,""));return c}if(t&&a!==null)throw Error(r(529,""));return null;case"script":return t=l.async,l=l.src,typeof l=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Ua(l),l=Il(n).hoistableScripts,a=l.get(t),a||(a={type:"script",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,e))}}function _a(e){return'href="'+gt(e)+'"'}function Mn(e){return'link[rel="stylesheet"]['+e+"]"}function vd(e){return T({},e,{"data-precedence":e.precedence,precedence:null})}function zg(e,t,l,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),Ve(t,"link",l),Ye(t),e.head.appendChild(t))}function Ua(e){return'[src="'+gt(e)+'"]'}function On(e){return"script[async]"+e}function pd(e,t,l){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+gt(l.href)+'"]');if(a)return t.instance=a,Ye(a),a;var n=T({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),Ye(a),Ve(a,"style",n),Vu(a,l.precedence,e),t.instance=a;case"stylesheet":n=_a(l.href);var u=e.querySelector(Mn(n));if(u)return t.state.loading|=4,t.instance=u,Ye(u),u;a=vd(l),(n=jt.get(n))&&fc(a,n),u=(e.ownerDocument||e).createElement("link"),Ye(u);var c=u;return c._p=new Promise(function(o,v){c.onload=o,c.onerror=v}),Ve(u,"link",a),t.state.loading|=4,Vu(u,l.precedence,e),t.instance=u;case"script":return u=Ua(l.src),(n=e.querySelector(On(u)))?(t.instance=n,Ye(n),n):(a=l,(n=jt.get(u))&&(a=T({},l),oc(a,n)),e=e.ownerDocument||e,n=e.createElement("script"),Ye(n),Ve(n,"link",a),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(r(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,Vu(a,l.precedence,e));return t.instance}function Vu(e,t,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,c=0;c<a.length;c++){var o=a[c];if(o.dataset.precedence===t)u=o;else if(u!==n)break}u?u.parentNode.insertBefore(e,u.nextSibling):(t=l.nodeType===9?l.head:l,t.insertBefore(e,t.firstChild))}function fc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function oc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Ku=null;function yd(e,t,l){if(Ku===null){var a=new Map,n=Ku=new Map;n.set(l,a)}else n=Ku,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(e))return a;for(a.set(e,null),l=l.getElementsByTagName(e),n=0;n<l.length;n++){var u=l[n];if(!(u[Ga]||u[Ke]||e==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var c=u.getAttribute(t)||"";c=e+c;var o=a.get(c);o?o.push(u):a.set(c,[u])}}return a}function xd(e,t,l){e=e.ownerDocument||e,e.head.insertBefore(l,t==="title"?e.querySelector("head > title"):null)}function Dg(e,t,l){if(l===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function bd(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Rn=null;function Cg(){}function _g(e,t,l){if(Rn===null)throw Error(r(475));var a=Rn;if(t.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=_a(l.href),u=e.querySelector(Mn(n));if(u){e=u._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=Ju.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=u,Ye(u);return}u=e.ownerDocument||e,l=vd(l),(n=jt.get(n))&&fc(l,n),u=u.createElement("link"),Ye(u);var c=u;c._p=new Promise(function(o,v){c.onload=o,c.onerror=v}),Ve(u,"link",l),t.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=Ju.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function Ug(){if(Rn===null)throw Error(r(475));var e=Rn;return e.stylesheets&&e.count===0&&dc(e,e.stylesheets),0<e.count?function(t){var l=setTimeout(function(){if(e.stylesheets&&dc(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(l)}}:null}function Ju(){if(this.count--,this.count===0){if(this.stylesheets)dc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var ku=null;function dc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,ku=new Map,t.forEach(Bg,e),ku=null,Ju.call(e))}function Bg(e,t){if(!(t.state.loading&4)){var l=ku.get(e);if(l)var a=l.get(null);else{l=new Map,ku.set(e,l);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var c=n[u];(c.nodeName==="LINK"||c.getAttribute("media")!=="not all")&&(l.set(c.dataset.precedence,c),a=c)}a&&l.set(null,a)}n=t.instance,c=n.getAttribute("data-precedence"),u=l.get(c)||a,u===a&&l.set(null,n),l.set(c,n),this.count++,a=Ju.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var zn={$$typeof:Q,Provider:null,Consumer:null,_currentValue:te,_currentValue2:te,_threadCount:0};function Hg(e,t,l,a,n,u,c,o){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ci(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ci(0),this.hiddenUpdates=ci(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=c,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=o,this.incompleteTransitions=new Map}function Sd(e,t,l,a,n,u,c,o,v,A,B,q){return e=new Hg(e,t,l,c,o,v,A,q),t=1,u===!0&&(t|=24),u=st(3,null,null,t),e.current=u,u.stateNode=e,t=Ji(),t.refCount++,e.pooledCache=t,t.refCount++,u.memoizedState={element:a,isDehydrated:l,cache:t},Wi(u),e}function jd(e){return e?(e=oa,e):oa}function Nd(e,t,l,a,n,u){n=jd(n),a.context===null?a.context=n:a.pendingContext=n,a=ll(t),a.payload={element:l},u=u===void 0?null:u,u!==null&&(a.callback=u),l=al(e,a,t),l!==null&&(dt(l,e,t),un(l,e,t))}function Td(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var l=e.retryLane;e.retryLane=l!==0&&l<t?l:t}}function hc(e,t){Td(e,t),(e=e.alternate)&&Td(e,t)}function Ed(e){if(e.tag===13){var t=fa(e,67108864);t!==null&&dt(t,e,67108864),hc(e,67108864)}}var $u=!0;function qg(e,t,l,a){var n=U.T;U.T=null;var u=Z.p;try{Z.p=2,mc(e,t,l,a)}finally{Z.p=u,U.T=n}}function wg(e,t,l,a){var n=U.T;U.T=null;var u=Z.p;try{Z.p=8,mc(e,t,l,a)}finally{Z.p=u,U.T=n}}function mc(e,t,l,a){if($u){var n=gc(a);if(n===null)tc(e,t,a,Fu,l),Md(e,a);else if(Gg(n,e,t,l,a))a.stopPropagation();else if(Md(e,a),t&4&&-1<Yg.indexOf(e)){for(;n!==null;){var u=Pl(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var c=Nl(u.pendingLanes);if(c!==0){var o=u;for(o.pendingLanes|=2,o.entangledLanes|=2;c;){var v=1<<31-ut(c);o.entanglements[1]|=v,c&=~v}Ct(u),(me&6)===0&&(_u=Mt()+500,Nn(0))}}break;case 13:o=fa(u,2),o!==null&&dt(o,u,2),Bu(),hc(u,2)}if(u=gc(a),u===null&&tc(e,t,a,Fu,l),u===n)break;n=u}n!==null&&a.stopPropagation()}else tc(e,t,a,null,l)}}function gc(e){return e=bi(e),vc(e)}var Fu=null;function vc(e){if(Fu=null,e=Wl(e),e!==null){var t=p(e);if(t===null)e=null;else{var l=t.tag;if(l===13){if(e=S(t),e!==null)return e;e=null}else if(l===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Fu=e,null}function Ad(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Th()){case wc:return 2;case Yc:return 8;case Xn:case Eh:return 32;case Gc:return 268435456;default:return 32}default:return 32}}var pc=!1,vl=null,pl=null,yl=null,Dn=new Map,Cn=new Map,xl=[],Yg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Md(e,t){switch(e){case"focusin":case"focusout":vl=null;break;case"dragenter":case"dragleave":pl=null;break;case"mouseover":case"mouseout":yl=null;break;case"pointerover":case"pointerout":Dn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Cn.delete(t.pointerId)}}function _n(e,t,l,a,n,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:l,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},t!==null&&(t=Pl(t),t!==null&&Ed(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function Gg(e,t,l,a,n){switch(t){case"focusin":return vl=_n(vl,e,t,l,a,n),!0;case"dragenter":return pl=_n(pl,e,t,l,a,n),!0;case"mouseover":return yl=_n(yl,e,t,l,a,n),!0;case"pointerover":var u=n.pointerId;return Dn.set(u,_n(Dn.get(u)||null,e,t,l,a,n)),!0;case"gotpointercapture":return u=n.pointerId,Cn.set(u,_n(Cn.get(u)||null,e,t,l,a,n)),!0}return!1}function Od(e){var t=Wl(e.target);if(t!==null){var l=p(t);if(l!==null){if(t=l.tag,t===13){if(t=S(l),t!==null){e.blockedOn=t,_h(e.priority,function(){if(l.tag===13){var a=ot();a=ri(a);var n=fa(l,a);n!==null&&dt(n,l,a),hc(l,a)}});return}}else if(t===3&&l.stateNode.current.memoizedState.isDehydrated){e.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Wu(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var l=gc(e.nativeEvent);if(l===null){l=e.nativeEvent;var a=new l.constructor(l.type,l);xi=a,l.target.dispatchEvent(a),xi=null}else return t=Pl(l),t!==null&&Ed(t),e.blockedOn=l,!1;t.shift()}return!0}function Rd(e,t,l){Wu(e)&&l.delete(t)}function Lg(){pc=!1,vl!==null&&Wu(vl)&&(vl=null),pl!==null&&Wu(pl)&&(pl=null),yl!==null&&Wu(yl)&&(yl=null),Dn.forEach(Rd),Cn.forEach(Rd)}function Pu(e,t){e.blockedOn===t&&(e.blockedOn=null,pc||(pc=!0,s.unstable_scheduleCallback(s.unstable_NormalPriority,Lg)))}var Iu=null;function zd(e){Iu!==e&&(Iu=e,s.unstable_scheduleCallback(s.unstable_NormalPriority,function(){Iu===e&&(Iu=null);for(var t=0;t<e.length;t+=3){var l=e[t],a=e[t+1],n=e[t+2];if(typeof a!="function"){if(vc(a||l)===null)continue;break}var u=Pl(l);u!==null&&(e.splice(t,3),t-=3,vs(u,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function Un(e){function t(v){return Pu(v,e)}vl!==null&&Pu(vl,e),pl!==null&&Pu(pl,e),yl!==null&&Pu(yl,e),Dn.forEach(t),Cn.forEach(t);for(var l=0;l<xl.length;l++){var a=xl[l];a.blockedOn===e&&(a.blockedOn=null)}for(;0<xl.length&&(l=xl[0],l.blockedOn===null);)Od(l),l.blockedOn===null&&xl.shift();if(l=(e.ownerDocument||e).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],u=l[a+1],c=n[Pe]||null;if(typeof u=="function")c||zd(l);else if(c){var o=null;if(u&&u.hasAttribute("formAction")){if(n=u,c=u[Pe]||null)o=c.formAction;else if(vc(n)!==null)continue}else o=c.action;typeof o=="function"?l[a+1]=o:(l.splice(a,3),a-=3),zd(l)}}}function yc(e){this._internalRoot=e}ei.prototype.render=yc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(r(409));var l=t.current,a=ot();Nd(l,a,e,t,null,null)},ei.prototype.unmount=yc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Nd(e.current,2,null,e,null,null),Bu(),t[Fl]=null}};function ei(e){this._internalRoot=e}ei.prototype.unstable_scheduleHydration=function(e){if(e){var t=Vc();e={blockedOn:null,target:e,priority:t};for(var l=0;l<xl.length&&t!==0&&t<xl[l].priority;l++);xl.splice(l,0,e),l===0&&Od(e)}};var Dd=f.version;if(Dd!=="19.1.1")throw Error(r(527,Dd,"19.1.1"));Z.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(r(188)):(e=Object.keys(e).join(","),Error(r(268,e)));return e=g(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var Xg={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:U,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ti=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ti.isDisabled&&ti.supportsFiber)try{qa=ti.inject(Xg),nt=ti}catch{}}return Hn.createRoot=function(e,t){if(!h(e))throw Error(r(299));var l=!1,a="",n=Kf,u=Jf,c=kf,o=null;return t!=null&&(t.unstable_strictMode===!0&&(l=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(u=t.onCaughtError),t.onRecoverableError!==void 0&&(c=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(o=t.unstable_transitionCallbacks)),t=Sd(e,1,!1,null,null,l,a,n,u,c,o,null),e[Fl]=t.current,ec(e),new yc(t)},Hn.hydrateRoot=function(e,t,l){if(!h(e))throw Error(r(299));var a=!1,n="",u=Kf,c=Jf,o=kf,v=null,A=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(c=l.onCaughtError),l.onRecoverableError!==void 0&&(o=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(v=l.unstable_transitionCallbacks),l.formState!==void 0&&(A=l.formState)),t=Sd(e,1,!0,t,l??null,a,n,u,c,o,v,A),t.context=jd(null),l=t.current,a=ot(),a=ri(a),n=ll(a),n.callback=null,al(l,n,a),l=a,t.current.lanes=l,Ya(t,l),Ct(t),e[Fl]=t.current,ec(e),new ei(t)},Hn.version="19.1.1",Hn}var Ld;function tv(){if(Ld)return Sc.exports;Ld=1;function s(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(s)}catch(f){console.error(f)}}return s(),Sc.exports=ev(),Sc.exports}var lv=tv();ah();/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function qn(){return qn=Object.assign?Object.assign.bind():function(s){for(var f=1;f<arguments.length;f++){var d=arguments[f];for(var r in d)Object.prototype.hasOwnProperty.call(d,r)&&(s[r]=d[r])}return s},qn.apply(this,arguments)}var Sl;(function(s){s.Pop="POP",s.Push="PUSH",s.Replace="REPLACE"})(Sl||(Sl={}));const Xd="popstate";function av(s){s===void 0&&(s={});function f(r,h){let{pathname:p,search:S,hash:_}=r.location;return Ac("",{pathname:p,search:S,hash:_},h.state&&h.state.usr||null,h.state&&h.state.key||"default")}function d(r,h){return typeof h=="string"?h:li(h)}return uv(f,d,null,s)}function Be(s,f){if(s===!1||s===null||typeof s>"u")throw new Error(f)}function nh(s,f){if(!s){typeof console<"u"&&console.warn(f);try{throw new Error(f)}catch{}}}function nv(){return Math.random().toString(36).substr(2,8)}function Qd(s,f){return{usr:s.state,key:s.key,idx:f}}function Ac(s,f,d,r){return d===void 0&&(d=null),qn({pathname:typeof s=="string"?s:s.pathname,search:"",hash:""},typeof f=="string"?Ba(f):f,{state:d,key:f&&f.key||r||nv()})}function li(s){let{pathname:f="/",search:d="",hash:r=""}=s;return d&&d!=="?"&&(f+=d.charAt(0)==="?"?d:"?"+d),r&&r!=="#"&&(f+=r.charAt(0)==="#"?r:"#"+r),f}function Ba(s){let f={};if(s){let d=s.indexOf("#");d>=0&&(f.hash=s.substr(d),s=s.substr(0,d));let r=s.indexOf("?");r>=0&&(f.search=s.substr(r),s=s.substr(0,r)),s&&(f.pathname=s)}return f}function uv(s,f,d,r){r===void 0&&(r={});let{window:h=document.defaultView,v5Compat:p=!1}=r,S=h.history,_=Sl.Pop,g=null,m=T();m==null&&(m=0,S.replaceState(qn({},S.state,{idx:m}),""));function T(){return(S.state||{idx:null}).idx}function y(){_=Sl.Pop;let M=T(),L=M==null?null:M-m;m=M,g&&g({action:_,location:G.location,delta:L})}function N(M,L){_=Sl.Push;let K=Ac(G.location,M,L);m=T()+1;let Q=Qd(K,m),W=G.createHref(K);try{S.pushState(Q,"",W)}catch(w){if(w instanceof DOMException&&w.name==="DataCloneError")throw w;h.location.assign(W)}p&&g&&g({action:_,location:G.location,delta:1})}function z(M,L){_=Sl.Replace;let K=Ac(G.location,M,L);m=T();let Q=Qd(K,m),W=G.createHref(K);S.replaceState(Q,"",W),p&&g&&g({action:_,location:G.location,delta:0})}function C(M){let L=h.location.origin!=="null"?h.location.origin:h.location.href,K=typeof M=="string"?M:li(M);return K=K.replace(/ $/,"%20"),Be(L,"No window.location.(origin|href) available to create URL for href: "+K),new URL(K,L)}let G={get action(){return _},get location(){return s(h,S)},listen(M){if(g)throw new Error("A history only accepts one active listener");return h.addEventListener(Xd,y),g=M,()=>{h.removeEventListener(Xd,y),g=null}},createHref(M){return f(h,M)},createURL:C,encodeLocation(M){let L=C(M);return{pathname:L.pathname,search:L.search,hash:L.hash}},push:N,replace:z,go(M){return S.go(M)}};return G}var Zd;(function(s){s.data="data",s.deferred="deferred",s.redirect="redirect",s.error="error"})(Zd||(Zd={}));function iv(s,f,d){return d===void 0&&(d="/"),sv(s,f,d)}function sv(s,f,d,r){let h=typeof f=="string"?Ba(f):f,p=Dc(h.pathname||"/",d);if(p==null)return null;let S=uh(s);cv(S);let _=null;for(let g=0;_==null&&g<S.length;++g){let m=bv(p);_=pv(S[g],m)}return _}function uh(s,f,d,r){f===void 0&&(f=[]),d===void 0&&(d=[]),r===void 0&&(r="");let h=(p,S,_)=>{let g={relativePath:_===void 0?p.path||"":_,caseSensitive:p.caseSensitive===!0,childrenIndex:S,route:p};g.relativePath.startsWith("/")&&(Be(g.relativePath.startsWith(r),'Absolute route path "'+g.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),g.relativePath=g.relativePath.slice(r.length));let m=jl([r,g.relativePath]),T=d.concat(g);p.children&&p.children.length>0&&(Be(p.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+m+'".')),uh(p.children,f,T,m)),!(p.path==null&&!p.index)&&f.push({path:m,score:gv(m,p.index),routesMeta:T})};return s.forEach((p,S)=>{var _;if(p.path===""||!((_=p.path)!=null&&_.includes("?")))h(p,S);else for(let g of ih(p.path))h(p,S,g)}),f}function ih(s){let f=s.split("/");if(f.length===0)return[];let[d,...r]=f,h=d.endsWith("?"),p=d.replace(/\?$/,"");if(r.length===0)return h?[p,""]:[p];let S=ih(r.join("/")),_=[];return _.push(...S.map(g=>g===""?p:[p,g].join("/"))),h&&_.push(...S),_.map(g=>s.startsWith("/")&&g===""?"/":g)}function cv(s){s.sort((f,d)=>f.score!==d.score?d.score-f.score:vv(f.routesMeta.map(r=>r.childrenIndex),d.routesMeta.map(r=>r.childrenIndex)))}const rv=/^:[\w-]+$/,fv=3,ov=2,dv=1,hv=10,mv=-2,Vd=s=>s==="*";function gv(s,f){let d=s.split("/"),r=d.length;return d.some(Vd)&&(r+=mv),f&&(r+=ov),d.filter(h=>!Vd(h)).reduce((h,p)=>h+(rv.test(p)?fv:p===""?dv:hv),r)}function vv(s,f){return s.length===f.length&&s.slice(0,-1).every((r,h)=>r===f[h])?s[s.length-1]-f[f.length-1]:0}function pv(s,f,d){let{routesMeta:r}=s,h={},p="/",S=[];for(let _=0;_<r.length;++_){let g=r[_],m=_===r.length-1,T=p==="/"?f:f.slice(p.length)||"/",y=yv({path:g.relativePath,caseSensitive:g.caseSensitive,end:m},T),N=g.route;if(!y)return null;Object.assign(h,y.params),S.push({params:h,pathname:jl([p,y.pathname]),pathnameBase:Tv(jl([p,y.pathnameBase])),route:N}),y.pathnameBase!=="/"&&(p=jl([p,y.pathnameBase]))}return S}function yv(s,f){typeof s=="string"&&(s={path:s,caseSensitive:!1,end:!0});let[d,r]=xv(s.path,s.caseSensitive,s.end),h=f.match(d);if(!h)return null;let p=h[0],S=p.replace(/(.)\/+$/,"$1"),_=h.slice(1);return{params:r.reduce((m,T,y)=>{let{paramName:N,isOptional:z}=T;if(N==="*"){let G=_[y]||"";S=p.slice(0,p.length-G.length).replace(/(.)\/+$/,"$1")}const C=_[y];return z&&!C?m[N]=void 0:m[N]=(C||"").replace(/%2F/g,"/"),m},{}),pathname:p,pathnameBase:S,pattern:s}}function xv(s,f,d){f===void 0&&(f=!1),d===void 0&&(d=!0),nh(s==="*"||!s.endsWith("*")||s.endsWith("/*"),'Route path "'+s+'" will be treated as if it were '+('"'+s.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+s.replace(/\*$/,"/*")+'".'));let r=[],h="^"+s.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(S,_,g)=>(r.push({paramName:_,isOptional:g!=null}),g?"/?([^\\/]+)?":"/([^\\/]+)"));return s.endsWith("*")?(r.push({paramName:"*"}),h+=s==="*"||s==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):d?h+="\\/*$":s!==""&&s!=="/"&&(h+="(?:(?=\\/|$))"),[new RegExp(h,f?void 0:"i"),r]}function bv(s){try{return s.split("/").map(f=>decodeURIComponent(f).replace(/\//g,"%2F")).join("/")}catch(f){return nh(!1,'The URL path "'+s+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+f+").")),s}}function Dc(s,f){if(f==="/")return s;if(!s.toLowerCase().startsWith(f.toLowerCase()))return null;let d=f.endsWith("/")?f.length-1:f.length,r=s.charAt(d);return r&&r!=="/"?null:s.slice(d)||"/"}function Sv(s,f){f===void 0&&(f="/");let{pathname:d,search:r="",hash:h=""}=typeof s=="string"?Ba(s):s;return{pathname:d?d.startsWith("/")?d:jv(d,f):f,search:Ev(r),hash:Av(h)}}function jv(s,f){let d=f.replace(/\/+$/,"").split("/");return s.split("/").forEach(h=>{h===".."?d.length>1&&d.pop():h!=="."&&d.push(h)}),d.length>1?d.join("/"):"/"}function Ec(s,f,d,r){return"Cannot include a '"+s+"' character in a manually specified "+("`to."+f+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+d+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Nv(s){return s.filter((f,d)=>d===0||f.route.path&&f.route.path.length>0)}function sh(s,f){let d=Nv(s);return f?d.map((r,h)=>h===d.length-1?r.pathname:r.pathnameBase):d.map(r=>r.pathnameBase)}function ch(s,f,d,r){r===void 0&&(r=!1);let h;typeof s=="string"?h=Ba(s):(h=qn({},s),Be(!h.pathname||!h.pathname.includes("?"),Ec("?","pathname","search",h)),Be(!h.pathname||!h.pathname.includes("#"),Ec("#","pathname","hash",h)),Be(!h.search||!h.search.includes("#"),Ec("#","search","hash",h)));let p=s===""||h.pathname==="",S=p?"/":h.pathname,_;if(S==null)_=d;else{let y=f.length-1;if(!r&&S.startsWith("..")){let N=S.split("/");for(;N[0]==="..";)N.shift(),y-=1;h.pathname=N.join("/")}_=y>=0?f[y]:"/"}let g=Sv(h,_),m=S&&S!=="/"&&S.endsWith("/"),T=(p||S===".")&&d.endsWith("/");return!g.pathname.endsWith("/")&&(m||T)&&(g.pathname+="/"),g}const jl=s=>s.join("/").replace(/\/\/+/g,"/"),Tv=s=>s.replace(/\/+$/,"").replace(/^\/*/,"/"),Ev=s=>!s||s==="?"?"":s.startsWith("?")?s:"?"+s,Av=s=>!s||s==="#"?"":s.startsWith("#")?s:"#"+s;function Mv(s){return s!=null&&typeof s.status=="number"&&typeof s.statusText=="string"&&typeof s.internal=="boolean"&&"data"in s}const rh=["post","put","patch","delete"];new Set(rh);const Ov=["get",...rh];new Set(Ov);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function wn(){return wn=Object.assign?Object.assign.bind():function(s){for(var f=1;f<arguments.length;f++){var d=arguments[f];for(var r in d)Object.prototype.hasOwnProperty.call(d,r)&&(s[r]=d[r])}return s},wn.apply(this,arguments)}const Cc=O.createContext(null),Rv=O.createContext(null),Zl=O.createContext(null),ai=O.createContext(null),kt=O.createContext({outlet:null,matches:[],isDataRoute:!1}),fh=O.createContext(null);function zv(s,f){let{relative:d}=f===void 0?{}:f;Yn()||Be(!1);let{basename:r,navigator:h}=O.useContext(Zl),{hash:p,pathname:S,search:_}=dh(s,{relative:d}),g=S;return r!=="/"&&(g=S==="/"?r:jl([r,S])),h.createHref({pathname:g,search:_,hash:p})}function Yn(){return O.useContext(ai)!=null}function At(){return Yn()||Be(!1),O.useContext(ai).location}function oh(s){O.useContext(Zl).static||O.useLayoutEffect(s)}function $e(){let{isDataRoute:s}=O.useContext(kt);return s?Vv():Dv()}function Dv(){Yn()||Be(!1);let s=O.useContext(Cc),{basename:f,future:d,navigator:r}=O.useContext(Zl),{matches:h}=O.useContext(kt),{pathname:p}=At(),S=JSON.stringify(sh(h,d.v7_relativeSplatPath)),_=O.useRef(!1);return oh(()=>{_.current=!0}),O.useCallback(function(m,T){if(T===void 0&&(T={}),!_.current)return;if(typeof m=="number"){r.go(m);return}let y=ch(m,JSON.parse(S),p,T.relative==="path");s==null&&f!=="/"&&(y.pathname=y.pathname==="/"?f:jl([f,y.pathname])),(T.replace?r.replace:r.push)(y,T.state,T)},[f,r,S,p,s])}const Cv=O.createContext(null);function _v(s){let f=O.useContext(kt).outlet;return f&&O.createElement(Cv.Provider,{value:s},f)}function Vl(){let{matches:s}=O.useContext(kt),f=s[s.length-1];return f?f.params:{}}function dh(s,f){let{relative:d}=f===void 0?{}:f,{future:r}=O.useContext(Zl),{matches:h}=O.useContext(kt),{pathname:p}=At(),S=JSON.stringify(sh(h,r.v7_relativeSplatPath));return O.useMemo(()=>ch(s,JSON.parse(S),p,d==="path"),[s,S,p,d])}function Uv(s,f){return Bv(s,f)}function Bv(s,f,d,r){Yn()||Be(!1);let{navigator:h}=O.useContext(Zl),{matches:p}=O.useContext(kt),S=p[p.length-1],_=S?S.params:{};S&&S.pathname;let g=S?S.pathnameBase:"/";S&&S.route;let m=At(),T;if(f){var y;let M=typeof f=="string"?Ba(f):f;g==="/"||(y=M.pathname)!=null&&y.startsWith(g)||Be(!1),T=M}else T=m;let N=T.pathname||"/",z=N;if(g!=="/"){let M=g.replace(/^\//,"").split("/");z="/"+N.replace(/^\//,"").split("/").slice(M.length).join("/")}let C=iv(s,{pathname:z}),G=Gv(C&&C.map(M=>Object.assign({},M,{params:Object.assign({},_,M.params),pathname:jl([g,h.encodeLocation?h.encodeLocation(M.pathname).pathname:M.pathname]),pathnameBase:M.pathnameBase==="/"?g:jl([g,h.encodeLocation?h.encodeLocation(M.pathnameBase).pathname:M.pathnameBase])})),p,d,r);return f&&G?O.createElement(ai.Provider,{value:{location:wn({pathname:"/",search:"",hash:"",state:null,key:"default"},T),navigationType:Sl.Pop}},G):G}function Hv(){let s=Zv(),f=Mv(s)?s.status+" "+s.statusText:s instanceof Error?s.message:JSON.stringify(s),d=s instanceof Error?s.stack:null,h={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return O.createElement(O.Fragment,null,O.createElement("h2",null,"Unexpected Application Error!"),O.createElement("h3",{style:{fontStyle:"italic"}},f),d?O.createElement("pre",{style:h},d):null,null)}const qv=O.createElement(Hv,null);class wv extends O.Component{constructor(f){super(f),this.state={location:f.location,revalidation:f.revalidation,error:f.error}}static getDerivedStateFromError(f){return{error:f}}static getDerivedStateFromProps(f,d){return d.location!==f.location||d.revalidation!=="idle"&&f.revalidation==="idle"?{error:f.error,location:f.location,revalidation:f.revalidation}:{error:f.error!==void 0?f.error:d.error,location:d.location,revalidation:f.revalidation||d.revalidation}}componentDidCatch(f,d){console.error("React Router caught the following error during render",f,d)}render(){return this.state.error!==void 0?O.createElement(kt.Provider,{value:this.props.routeContext},O.createElement(fh.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Yv(s){let{routeContext:f,match:d,children:r}=s,h=O.useContext(Cc);return h&&h.static&&h.staticContext&&(d.route.errorElement||d.route.ErrorBoundary)&&(h.staticContext._deepestRenderedBoundaryId=d.route.id),O.createElement(kt.Provider,{value:f},r)}function Gv(s,f,d,r){var h;if(f===void 0&&(f=[]),d===void 0&&(d=null),r===void 0&&(r=null),s==null){var p;if(!d)return null;if(d.errors)s=d.matches;else if((p=r)!=null&&p.v7_partialHydration&&f.length===0&&!d.initialized&&d.matches.length>0)s=d.matches;else return null}let S=s,_=(h=d)==null?void 0:h.errors;if(_!=null){let T=S.findIndex(y=>y.route.id&&_?.[y.route.id]!==void 0);T>=0||Be(!1),S=S.slice(0,Math.min(S.length,T+1))}let g=!1,m=-1;if(d&&r&&r.v7_partialHydration)for(let T=0;T<S.length;T++){let y=S[T];if((y.route.HydrateFallback||y.route.hydrateFallbackElement)&&(m=T),y.route.id){let{loaderData:N,errors:z}=d,C=y.route.loader&&N[y.route.id]===void 0&&(!z||z[y.route.id]===void 0);if(y.route.lazy||C){g=!0,m>=0?S=S.slice(0,m+1):S=[S[0]];break}}}return S.reduceRight((T,y,N)=>{let z,C=!1,G=null,M=null;d&&(z=_&&y.route.id?_[y.route.id]:void 0,G=y.route.errorElement||qv,g&&(m<0&&N===0?(Kv("route-fallback"),C=!0,M=null):m===N&&(C=!0,M=y.route.hydrateFallbackElement||null)));let L=f.concat(S.slice(0,N+1)),K=()=>{let Q;return z?Q=G:C?Q=M:y.route.Component?Q=O.createElement(y.route.Component,null):y.route.element?Q=y.route.element:Q=T,O.createElement(Yv,{match:y,routeContext:{outlet:T,matches:L,isDataRoute:d!=null},children:Q})};return d&&(y.route.ErrorBoundary||y.route.errorElement||N===0)?O.createElement(wv,{location:d.location,revalidation:d.revalidation,component:G,error:z,children:K(),routeContext:{outlet:null,matches:L,isDataRoute:!0}}):K()},null)}var hh=(function(s){return s.UseBlocker="useBlocker",s.UseRevalidator="useRevalidator",s.UseNavigateStable="useNavigate",s})(hh||{}),mh=(function(s){return s.UseBlocker="useBlocker",s.UseLoaderData="useLoaderData",s.UseActionData="useActionData",s.UseRouteError="useRouteError",s.UseNavigation="useNavigation",s.UseRouteLoaderData="useRouteLoaderData",s.UseMatches="useMatches",s.UseRevalidator="useRevalidator",s.UseNavigateStable="useNavigate",s.UseRouteId="useRouteId",s})(mh||{});function Lv(s){let f=O.useContext(Cc);return f||Be(!1),f}function Xv(s){let f=O.useContext(Rv);return f||Be(!1),f}function Qv(s){let f=O.useContext(kt);return f||Be(!1),f}function gh(s){let f=Qv(),d=f.matches[f.matches.length-1];return d.route.id||Be(!1),d.route.id}function Zv(){var s;let f=O.useContext(fh),d=Xv(),r=gh();return f!==void 0?f:(s=d.errors)==null?void 0:s[r]}function Vv(){let{router:s}=Lv(hh.UseNavigateStable),f=gh(mh.UseNavigateStable),d=O.useRef(!1);return oh(()=>{d.current=!0}),O.useCallback(function(h,p){p===void 0&&(p={}),d.current&&(typeof h=="number"?s.navigate(h):s.navigate(h,wn({fromRouteId:f},p)))},[s,f])}const Kd={};function Kv(s,f,d){Kd[s]||(Kd[s]=!0)}function Jv(s,f){s?.v7_startTransition,s?.v7_relativeSplatPath}function kv(s){return _v(s.context)}function Se(s){Be(!1)}function $v(s){let{basename:f="/",children:d=null,location:r,navigationType:h=Sl.Pop,navigator:p,static:S=!1,future:_}=s;Yn()&&Be(!1);let g=f.replace(/^\/*/,"/"),m=O.useMemo(()=>({basename:g,navigator:p,static:S,future:wn({v7_relativeSplatPath:!1},_)}),[g,_,p,S]);typeof r=="string"&&(r=Ba(r));let{pathname:T="/",search:y="",hash:N="",state:z=null,key:C="default"}=r,G=O.useMemo(()=>{let M=Dc(T,g);return M==null?null:{location:{pathname:M,search:y,hash:N,state:z,key:C},navigationType:h}},[g,T,y,N,z,C,h]);return G==null?null:O.createElement(Zl.Provider,{value:m},O.createElement(ai.Provider,{children:d,value:G}))}function Fv(s){let{children:f,location:d}=s;return Uv(Mc(f),d)}new Promise(()=>{});function Mc(s,f){f===void 0&&(f=[]);let d=[];return O.Children.forEach(s,(r,h)=>{if(!O.isValidElement(r))return;let p=[...f,h];if(r.type===O.Fragment){d.push.apply(d,Mc(r.props.children,p));return}r.type!==Se&&Be(!1),!r.props.index||!r.props.children||Be(!1);let S={id:r.props.id||p.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(S.children=Mc(r.props.children,p)),d.push(S)}),d}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Oc(){return Oc=Object.assign?Object.assign.bind():function(s){for(var f=1;f<arguments.length;f++){var d=arguments[f];for(var r in d)Object.prototype.hasOwnProperty.call(d,r)&&(s[r]=d[r])}return s},Oc.apply(this,arguments)}function Wv(s,f){if(s==null)return{};var d={},r=Object.keys(s),h,p;for(p=0;p<r.length;p++)h=r[p],!(f.indexOf(h)>=0)&&(d[h]=s[h]);return d}function Pv(s){return!!(s.metaKey||s.altKey||s.ctrlKey||s.shiftKey)}function Iv(s,f){return s.button===0&&(!f||f==="_self")&&!Pv(s)}const ep=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],tp="6";try{window.__reactRouterVersion=tp}catch{}const lp="startTransition",Jd=Fg[lp];function ap(s){let{basename:f,children:d,future:r,window:h}=s,p=O.useRef();p.current==null&&(p.current=av({window:h,v5Compat:!0}));let S=p.current,[_,g]=O.useState({action:S.action,location:S.location}),{v7_startTransition:m}=r||{},T=O.useCallback(y=>{m&&Jd?Jd(()=>g(y)):g(y)},[g,m]);return O.useLayoutEffect(()=>S.listen(T),[S,T]),O.useEffect(()=>Jv(r),[r]),O.createElement($v,{basename:f,children:d,location:_.location,navigationType:_.action,navigator:S,future:r})}const np=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",up=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ll=O.forwardRef(function(f,d){let{onClick:r,relative:h,reloadDocument:p,replace:S,state:_,target:g,to:m,preventScrollReset:T,viewTransition:y}=f,N=Wv(f,ep),{basename:z}=O.useContext(Zl),C,G=!1;if(typeof m=="string"&&up.test(m)&&(C=m,np))try{let Q=new URL(window.location.href),W=m.startsWith("//")?new URL(Q.protocol+m):new URL(m),w=Dc(W.pathname,z);W.origin===Q.origin&&w!=null?m=w+W.search+W.hash:G=!0}catch{}let M=zv(m,{relative:h}),L=ip(m,{replace:S,state:_,target:g,preventScrollReset:T,relative:h,viewTransition:y});function K(Q){r&&r(Q),Q.defaultPrevented||L(Q)}return O.createElement("a",Oc({},N,{href:C||M,onClick:G||p?r:K,ref:d,target:g}))});var kd;(function(s){s.UseScrollRestoration="useScrollRestoration",s.UseSubmit="useSubmit",s.UseSubmitFetcher="useSubmitFetcher",s.UseFetcher="useFetcher",s.useViewTransitionState="useViewTransitionState"})(kd||(kd={}));var $d;(function(s){s.UseFetcher="useFetcher",s.UseFetchers="useFetchers",s.UseScrollRestoration="useScrollRestoration"})($d||($d={}));function ip(s,f){let{target:d,replace:r,state:h,preventScrollReset:p,relative:S,viewTransition:_}=f===void 0?{}:f,g=$e(),m=At(),T=dh(s,{relative:S});return O.useCallback(y=>{if(Iv(y,d)){y.preventDefault();let N=r!==void 0?r:li(m)===li(T);g(s,{replace:N,state:h,preventScrollReset:p,relative:S,viewTransition:_})}},[m,g,T,r,h,d,s,p,S,_])}const _c="http://localhost:8090";function vh(){const s=Uc();return s?{Authorization:`Bearer ${s}`}:{}}async function sp(s,f){const d=await fetch(`${_c}/api/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:s,password:f})});if(!d.ok){const S=await d.text();throw new Error(S||d.statusText)}const r=await d.json(),h=r.token||r.accessToken||r.jwt,p=r.user||(r.name||r.role||r.username?{name:r.name,role:r.role,username:r.username}:null);return{token:h,user:p,raw:r}}function cp(s){s&&localStorage.setItem("authToken",s)}function Uc(){return localStorage.getItem("authToken")}async function rp(){if(!Uc())return!1;const f=await fetch(`${_c}/api/auth/validate-token`,{method:"POST",headers:{"Content-Type":"application/json",...vh()}});if(!f.ok)return!1;try{return await f.json()}catch{return!0}}async function fp(){const s=await fetch(`${_c}/api/auth/roles`,{method:"GET",headers:{"Content-Type":"application/json",...vh()}});if(!s.ok){const f=await s.text();throw new Error(f||s.statusText)}return s.json()}function op(s){if(!s)return null;try{const f=s.split(".");if(f.length<2)return null;const d=f[1],r=decodeURIComponent(escape(window.atob(d)));return JSON.parse(r)}catch{return null}}const Xl={login:sp,setToken:cp,getToken:Uc,validateToken:rp,getRoles:fp,parseJwt:op};function dp(){const[s,f]=O.useState(""),[d,r]=O.useState(""),[h,p]=O.useState(""),S=$e();async function _(g){g.preventDefault(),p("");try{const{token:m,user:T,raw:y}=await Xl.login(s,d);if(!m)throw new Error("No token returned");Xl.setToken(m);const N=T||y||{},z=N.name||N.username||s,C=N.role||N.roles||N.authorities;let G=null;if(Array.isArray(C)?G=C[0]:typeof C=="string"&&(G=C),!G){const M=Xl.parseJwt(m)||{},L=M.role||M.roles||M.authorities;Array.isArray(L)?G=L[0]:typeof L=="string"&&(G=L)}G&&G.toLowerCase().includes("admin")?S("/admin",{state:{name:z,role:G}}):G&&G.toLowerCase().includes("passenger")?S("/passenger",{state:{name:z,role:G}}):S("/staff",{state:{name:z,role:G||"staff"}})}catch(m){p(m.message||"Login failed")}}return i.jsx("div",{className:"min-h-screen flex items-center justify-center bg-slate-50",children:i.jsxs("form",{onSubmit:_,className:"w-full max-w-sm bg-white p-6 rounded shadow",children:[i.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Airline Management — Login"}),h&&i.jsx("div",{className:"mb-3 text-sm text-red-600",children:h}),i.jsxs("label",{className:"block mb-2",children:[i.jsx("span",{className:"text-sm",children:"Username"}),i.jsx("input",{value:s,onChange:g=>f(g.target.value),className:"mt-1 block w-full rounded border-gray-200 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50",placeholder:"username"})]}),i.jsxs("label",{className:"block mb-4",children:[i.jsx("span",{className:"text-sm",children:"Password"}),i.jsx("input",{type:"password",value:d,onChange:g=>r(g.target.value),className:"mt-1 block w-full rounded border-gray-200 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50",placeholder:"password"})]}),i.jsx("button",{type:"submit",className:"w-full bg-indigo-600 text-white py-2 rounded hover:bg-indigo-700",children:"Sign in"})]})})}function hp(){const s=$e(),d=At().pathname.replace(/\/$/,"")==="/admin";return i.jsxs("div",{className:"bg-white p-6 rounded shadow",children:[d?i.jsxs("div",{className:"flex gap-4 mb-6",children:[i.jsx("button",{onClick:()=>s("passengers"),className:"px-4 py-2 rounded bg-gray-100",children:"Manage Passengers"}),i.jsx("button",{onClick:()=>s("flights"),className:"px-4 py-2 rounded bg-gray-100",children:"Manage Flights"}),i.jsx("button",{onClick:()=>s("routes"),className:"px-4 py-2 rounded bg-gray-100",children:"Manage Routes"})]}):i.jsx("div",{className:"mb-4",children:i.jsx("button",{onClick:()=>s("/admin"),className:"px-3 py-1 rounded bg-gray-200",children:"Back to Dashboard"})}),i.jsx("div",{className:"text-sm text-gray-600",children:d?"Select an area to manage.":"Use the back button to return to the admin dashboard."})]})}function mp(){const{state:s}=At(),f=s?.name||"Admin";return i.jsx("div",{className:"min-h-screen bg-slate-50",children:i.jsxs("div",{className:"container mx-auto p-6",children:[i.jsxs("h1",{className:"text-2xl font-semibold mb-4",children:["Welcome admin ",f]}),i.jsx(hp,{}),i.jsx("div",{className:"mt-6",children:i.jsx(kv,{})})]})})}const gp="http://localhost:8090";async function Gn(s,f={}){const d={"Content-Type":"application/json",...f.headers||{}},r=Xl.getToken();r&&(d.Authorization=`Bearer ${r}`);const h=await fetch(`${gp}${s}`,{...f,headers:d});if(!h.ok){const S=await h.text();throw new Error(S||h.statusText)}const p=await h.text();return p?JSON.parse(p):null}async function Kl(){const s=await Gn("/flights",{method:"GET"});return s?Array.isArray(s)?s.map(Rc):[Rc(s)]:[]}async function Jl(s){const f=await Gn(`/flights/${s}`,{method:"GET"});return f?Rc(f):null}async function vp(s){return Gn("/flights",{method:"POST",body:JSON.stringify(s)})}async function pp(s,f){return Gn(`/flights/${s}`,{method:"PUT",body:JSON.stringify(f)})}async function yp(s){return Gn(`/flights/${s}`,{method:"DELETE"})}function Rc(s){return s&&{id:s.flightId||s.id,name:s.flightName||s.name,date:s.flightDate||s.date,route:s.route,departureTime:s.departureTime,arrivalTime:s.arrivalTime,totalSeats:s.totalSeats||s.totalSeats,availableSeats:s.availableSeats,services:s.services||(s.servicesJson?JSON.parse(s.servicesJson):[]),serviceSubtypes:s.serviceSubtypes||(s.serviceSubtypesJson?JSON.parse(s.serviceSubtypesJson):{}),seatMap:s.seatMap||(s.seatMapJson?JSON.parse(s.seatMapJson):[]),aircraftType:s.aircraftType}}function xp({passengers:s,flights:f}){function d(r){const h=f.find(p=>p.id===r);return h?`${h.name} (${h.route})`:"Unknown"}return i.jsx("div",{className:"overflow-x-auto",children:i.jsxs("table",{className:"w-full table-auto border-collapse",children:[i.jsx("thead",{children:i.jsxs("tr",{className:"text-left border-b",children:[i.jsx("th",{className:"p-2",children:"Name"}),i.jsx("th",{className:"p-2",children:"Flight"}),i.jsx("th",{className:"p-2",children:"Seat"}),i.jsx("th",{className:"p-2",children:"Ancillary Services"}),i.jsx("th",{className:"p-2",children:"Passport"}),i.jsx("th",{className:"p-2",children:"DOB"}),i.jsx("th",{className:"p-2",children:"Address"})]})}),i.jsx("tbody",{children:s.map(r=>i.jsxs("tr",{className:"border-b",children:[i.jsx("td",{className:"p-2",children:i.jsx(Ll,{to:encodeURI(`/admin/passengers/${r.name}`),className:"text-indigo-600",children:r.name})}),i.jsx("td",{className:"p-2",children:d(r.flightId)}),i.jsx("td",{className:"p-2",children:r.seat||"-"}),i.jsx("td",{className:"p-2",children:(r.services||[]).join(", ")||"-"}),i.jsx("td",{className:"p-2",children:r.passportNumber||"-"}),i.jsx("td",{className:"p-2",children:r.dateOfBirth||"-"}),i.jsx("td",{className:"p-2",children:r.address||"-"})]},r.id))})]})})}function bp({initial:s={},onSave:f,onCancel:d}){const[r,h]=O.useState({id:null,flightId:s.flightId||"",name:s.name||"",passportNumber:s.passportNumber||"",address:s.address||"",dateOfBirth:s.dateOfBirth||"",services:s.services||[],seat:s.seat||""});O.useEffect(()=>{h({id:s.id||null,flightId:s.flightId||"",name:s.name||"",passportNumber:s.passportNumber||"",address:s.address||"",dateOfBirth:s.dateOfBirth||"",services:s.services||[],seat:s.seat||""})},[s]);function p(g,m){h(T=>({...T,[g]:m}))}function S(g){h(m=>({...m,services:m.services.includes(g)?m.services.filter(T=>T!==g):[...m.services,g]}))}function _(g){g.preventDefault(),f(r)}return i.jsxs("form",{onSubmit:_,className:"grid grid-cols-2 gap-3",children:[i.jsxs("div",{children:[i.jsx("label",{className:"block",children:"Name"}),i.jsx("input",{value:r.name,onChange:g=>p("name",g.target.value),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block",children:"Flight ID"}),i.jsx("input",{value:r.flightId,onChange:g=>p("flightId",Number(g.target.value)),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block",children:"Passport Number"}),i.jsx("input",{value:r.passportNumber,onChange:g=>p("passportNumber",g.target.value),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block",children:"Date of Birth"}),i.jsx("input",{type:"date",value:r.dateOfBirth,onChange:g=>p("dateOfBirth",g.target.value),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{className:"col-span-2",children:[i.jsx("label",{className:"block",children:"Address"}),i.jsx("input",{value:r.address,onChange:g=>p("address",g.target.value),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block",children:"Seat"}),i.jsx("input",{value:r.seat,onChange:g=>p("seat",g.target.value),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block",children:"Services"}),i.jsx("div",{className:"flex gap-2 mt-1",children:["Meal","Ancillary","Shopping"].map(g=>i.jsxs("label",{className:"inline-flex items-center gap-2",children:[i.jsx("input",{type:"checkbox",checked:r.services.includes(g),onChange:()=>S(g)}),i.jsx("span",{children:g})]},g))})]}),i.jsxs("div",{className:"col-span-2 flex gap-2 justify-end mt-2",children:[i.jsx("button",{type:"button",className:"px-3 py-1 bg-gray-200 rounded",onClick:d,children:"Cancel"}),i.jsx("button",{type:"submit",className:"px-3 py-1 bg-indigo-600 text-white rounded",children:"Save"})]})]})}const Sp="http://localhost:8090";async function kl(s,f={}){const d={"Content-Type":"application/json",...f.headers||{}},r=Xl.getToken();r&&(d.Authorization=`Bearer ${r}`);const h=await fetch(`${Sp}${s}`,{...f,headers:d});if(!h.ok){const S=await h.text();throw new Error(S||h.statusText)}const p=await h.text();return p?JSON.parse(p):null}function Ql(s){return s&&{id:s.passengerId||s.id,flightId:s.flightId||s.flight||null,name:s.name,phoneNumber:s.phoneNumber||s.phone||s.contact,address:s.address,passportNumber:s.passportNumber||s.passport||null,dateOfBirth:s.dateOfBirth||s.dob||null,from:s.from,to:s.to,services:s.services||(s.servicesJson?Fd(s.servicesJson):[]),mealType:s.mealType,extraBaggage:s.extraBaggage,shoppingItems:s.shoppingItems||(s.shoppingItemsJson?Fd(s.shoppingItemsJson):[]),seat:s.seat||null,checkedIn:s.checkedIn||!1,wheelchair:s.wheelchair||!1,infant:s.infant||!1,email:s.email}}function Fd(s){try{return JSON.parse(s)}catch{return null}}async function ph(){const s=await kl("/passengers",{method:"GET"});return s?Array.isArray(s)?s.map(Ql):[Ql(s)]:[]}async function jp(s){const f=await kl(`/passengers/${s}`,{method:"GET"});return f?Ql(f):null}async function Bc(s){const f=await kl("/passengers",{method:"POST",body:JSON.stringify(s)});return f&&Ql(f)}async function Hc(s,f){const d=await kl(`/passengers/${s}`,{method:"PUT",body:JSON.stringify(f)});return d&&Ql(d)}async function Np(s){return kl(`/passengers/${s}`,{method:"DELETE"})}async function qc(s,f={}){const d=new URLSearchParams;f.checkedIn!==void 0&&d.set("checkedIn",String(f.checkedIn)),f.specialNeeds!==void 0&&d.set("specialNeeds",String(f.specialNeeds)),f.missingInfo!==void 0&&d.set("missingInfo",String(f.missingInfo));const r=d.toString()?`?${d.toString()}`:"",h=await kl(`/passengers/flight/${s}${r}`,{method:"GET"});return Array.isArray(h)?h.map(Ql):[]}async function yh(s){const f=await kl(`/passengers/search?name=${encodeURIComponent(s)}`,{method:"GET"});return Array.isArray(f)?f.map(Ql):[]}function Wd(){const[s,f]=O.useState([]),[d,r]=O.useState(!1),[h,p]=O.useState(""),[S,_]=O.useState("All"),[g,m]=O.useState(null),T=Vl(),y=$e(),N=T.flightId?Number(T.flightId):null;async function z(){try{const w=N?await qc(N):await ph();f(w)}catch(w){console.error("Failed to load passengers",w),f([])}}const[C,G]=O.useState([]);O.useEffect(()=>{let w=!0;async function fe(){try{const Ce=await Kl();if(!w)return;G(Ce)}catch(Ce){console.error("Failed to load flights",Ce),G([])}}return fe(),()=>{w=!1}},[]);async function M(w){try{w.id?await Hc(w.id,w):await Bc(w),m(null),await z()}catch(fe){console.error("Failed to save passenger",fe)}}O.useEffect(()=>{z()},[N]);const K=(d?s.filter(w=>!w.passportNumber||!w.address||!w.dateOfBirth):s).filter(w=>{if(!h)return!0;const fe=h.toLowerCase();return(w.name||"").toLowerCase().includes(fe)||(w.passportNumber||"").toLowerCase().includes(fe)||(w.seat||"").toLowerCase().includes(fe)||(w.phoneNumber||"").toLowerCase().includes(fe)}),Q=N?K.filter(w=>w.flightId===N&&(S==="All"?!0:S==="Checked-In"?!!w.checkedIn:S==="Wheelchair"?!!w.wheelchair:S==="Infant"?!!w.infant:!0)):[];function W(){y("/admin/passengers")}return i.jsx("div",{children:N?i.jsxs("div",{children:[i.jsxs("div",{className:"flex items-center justify-between mb-4",children:[i.jsxs("div",{className:"flex items-center gap-3",children:[i.jsx("button",{className:"text-sm text-indigo-600",onClick:W,children:"← Back to flights"}),i.jsx("input",{value:h,onChange:w=>p(w.target.value),placeholder:"Search passengers (name, passport, seat, phone)",className:"border p-2 rounded w-80"}),i.jsxs("label",{className:"flex items-center gap-2",children:[i.jsx("span",{className:"text-sm",children:"Show"}),i.jsxs("select",{value:S,onChange:w=>_(w.target.value),className:"border p-2 rounded",children:[i.jsx("option",{children:"All"}),i.jsx("option",{children:"Checked-In"}),i.jsx("option",{children:"Wheelchair"}),i.jsx("option",{children:"Infant"})]})]}),i.jsxs("label",{className:"flex items-center gap-2",children:[i.jsx("input",{type:"checkbox",checked:d,onChange:w=>r(w.target.checked)}),i.jsx("span",{children:"Show passengers missing mandatory info (passport / address / dob)"})]})]}),i.jsx("div",{children:i.jsx("button",{className:"px-3 py-1 bg-green-500 text-white rounded",onClick:()=>m({flightId:N}),children:"Add Passenger"})})]}),i.jsx(xp,{passengers:Q,flights:C}),g!==null&&i.jsx("div",{className:"mt-4 bg-gray-50 p-4 rounded",children:i.jsx(bp,{initial:g,onCancel:()=>m(null),onSave:M})})]}):i.jsxs("div",{children:[i.jsx("h2",{className:"text-xl font-semibold mb-3",children:"Flights"}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:C.map(w=>i.jsx("div",{className:"border rounded p-3 flex items-center justify-between",children:i.jsxs("div",{children:[i.jsx("div",{className:"font-medium",children:i.jsxs(Ll,{to:`/admin/passengers/${w.id}/passengerlist`,className:"text-indigo-600",children:[w.name," — ",w.route]})}),i.jsxs("div",{className:"text-sm text-gray-600",children:[w.date," • ",w.departureTime," → ",w.arrivalTime]}),i.jsxs("div",{className:"text-sm text-gray-600",children:["Available seats: ",w.availableSeats,"/",w.totalSeats]})]})},w.id))})]})})}function Tp(){const[s,f]=O.useState([]),[d,r]=O.useState(""),h=$e();O.useEffect(()=>{let S=!0;return Kl().then(_=>{S&&f(_||[])}).catch(()=>{S&&f([])}),()=>{S=!1}},[]);const p=s.filter(S=>{if(!d)return!0;const _=d.toLowerCase();return S.name?.toLowerCase().includes(_)||S.route?.toLowerCase().includes(_)});return i.jsxs("div",{children:[i.jsxs("div",{className:"flex justify-between items-center mb-3",children:[i.jsxs("div",{className:"flex items-center gap-3",children:[i.jsx("h2",{className:"text-lg font-semibold",children:"Flights"}),i.jsx("input",{value:d,onChange:S=>r(S.target.value),placeholder:"Search flights by name or route",className:"border p-2 rounded w-80"})]}),i.jsx("button",{className:"px-3 py-1 bg-green-500 text-white rounded",onClick:()=>h("/admin/flights/new"),children:"Add Flight"})]}),i.jsx("div",{className:"overflow-x-auto bg-white rounded p-2",children:i.jsxs("table",{className:"w-full table-auto border-collapse",children:[i.jsx("thead",{children:i.jsxs("tr",{className:"text-left border-b",children:[i.jsx("th",{className:"p-2",children:"Name"}),i.jsx("th",{className:"p-2",children:"Route"}),i.jsx("th",{className:"p-2",children:"Date"}),i.jsx("th",{className:"p-2",children:"Services"})]})}),i.jsx("tbody",{children:p.map(S=>i.jsxs("tr",{className:"border-b",children:[i.jsx("td",{className:"p-2",children:i.jsx(Ll,{to:`/admin/flights/${S.id}`,className:"text-indigo-600",children:S.name})}),i.jsx("td",{className:"p-2",children:S.route}),i.jsx("td",{className:"p-2",children:S.date||"-"}),i.jsx("td",{className:"p-2",children:(S.services||[]).join(", ")||"-"})]},S.id))})]})})]})}function Ep({onSave:s,onCancel:f}){const[d,r]=O.useState({departurePlace:"",arrivalPlace:""});return i.jsxs("form",{onSubmit:h=>{h.preventDefault(),s(d)},className:"grid grid-cols-2 gap-2 mb-3",children:[i.jsx("input",{placeholder:"Departure place",value:d.departurePlace,onChange:h=>r(p=>({...p,departurePlace:h.target.value})),className:"border p-2 rounded"}),i.jsx("input",{placeholder:"Arrival place",value:d.arrivalPlace,onChange:h=>r(p=>({...p,arrivalPlace:h.target.value})),className:"border p-2 rounded"}),i.jsxs("div",{className:"col-span-2 flex gap-2 justify-end",children:[i.jsx("button",{type:"button",onClick:f,className:"px-3 py-1 bg-gray-200 rounded",children:"Cancel"}),i.jsx("button",{type:"submit",className:"px-3 py-1 bg-green-500 text-white rounded",children:"Add Route"})]})]})}function Ap(){const[s,f]=O.useState([]);O.useEffect(()=>{let m=!0;async function T(){try{const y=await Kl();if(!m)return;f((y||[]).map(N=>{const z=(N.route||"").split("-");return{id:N.id,departurePlace:z[0]||"",arrivalPlace:z[1]||""}}))}catch(y){console.error("Failed to load routes",y)}}return T(),()=>{m=!1}},[]);const[d,r]=O.useState(!1),[h,p]=O.useState("");function S(m,T,y){f(N=>N.map(z=>z.id===m?{...z,[T]:y}:z))}function _({departurePlace:m,arrivalPlace:T}){const y=Math.max(0,...s.map(N=>N.id))+1;f(N=>[...N,{id:y,departurePlace:m,arrivalPlace:T}]),r(!1)}const g=s.filter(m=>{if(!h)return!0;const T=h.toLowerCase();return m.departurePlace?.toLowerCase().includes(T)||m.arrivalPlace?.toLowerCase().includes(T)});return i.jsxs("div",{children:[i.jsxs("div",{className:"flex justify-between items-center mb-3",children:[i.jsxs("div",{className:"flex items-center gap-3",children:[i.jsx("h2",{className:"text-lg font-semibold",children:"Routes"}),i.jsx("input",{value:h,onChange:m=>p(m.target.value),placeholder:"Search routes by departure or arrival",className:"border p-2 rounded w-80"})]}),i.jsx("button",{className:"px-3 py-1 bg-green-500 text-white rounded",onClick:()=>r(!0),children:"Add Route"})]}),d&&i.jsx(Ep,{onSave:_,onCancel:()=>r(!1)}),i.jsx("div",{className:"overflow-x-auto",children:i.jsxs("table",{className:"w-full table-auto border-collapse",children:[i.jsx("thead",{children:i.jsxs("tr",{className:"text-left border-b",children:[i.jsx("th",{className:"p-2",children:"Departure"}),i.jsx("th",{className:"p-2",children:"Arrival"})]})}),i.jsx("tbody",{children:g.map(m=>i.jsxs("tr",{className:"border-b",children:[i.jsx("td",{className:"p-2",children:i.jsx("input",{value:m.departurePlace,onChange:T=>S(m.id,"departurePlace",T.target.value),className:"w-full border p-1 rounded"})}),i.jsx("td",{className:"p-2",children:i.jsx("input",{value:m.arrivalPlace,onChange:T=>S(m.id,"arrivalPlace",T.target.value),className:"w-full border p-1 rounded"})})]},m.id))})]})})]})}const Mp="http://localhost:8090";async function xh(s,f={}){const d={"Content-Type":"application/json",...f.headers||{}},r=Xl.getToken();r&&(d.Authorization=`Bearer ${r}`);const h=await fetch(`${Mp}${s}`,{...f,headers:d});if(!h.ok){const S=await h.text();throw new Error(S||h.statusText)}const p=await h.text();return p?JSON.parse(p):null}async function Op(){const s=await xh("/history",{method:"GET"});return Array.isArray(s)?s:[]}async function Rp(s){return xh(`/history/passenger/${s}`,{method:"GET"})}function zp(){const{name:s}=Vl(),f=$e(),[d,r]=O.useState(null),[h,p]=O.useState(!0);O.useEffect(()=>{let N=!0;async function z(){p(!0);try{const C=await yh(s);if(!N)return;r(C&&C.length>0?{...C[0]}:null)}catch(C){console.error("Failed to load passenger",C),N&&r(null)}finally{N&&p(!1)}}return z(),()=>{N=!1}},[s]);const[S,_]=O.useState([]),[g,m]=O.useState(new Map);if(O.useEffect(()=>{let N=!0;async function z(){if(d)try{const C=await Rp(d.id);if(!N)return;let G=[];C?Array.isArray(C)?G=C:Array.isArray(C.travelHistory)?G=C.travelHistory:G=[]:G=[],_(G);const M=Array.from(new Set(G.map(Q=>Q.flightId).filter(Boolean))),L=await Promise.all(M.map(Q=>Jl(Q)));if(!N)return;const K=new Map;L.forEach(Q=>{Q&&K.set(Q.id,Q)}),m(K)}catch(C){console.error("Failed to load travel history",C)}}return z(),()=>{N=!1}},[d]),h)return i.jsx("div",{className:"p-4",children:"Loading..."});if(!d)return i.jsx("div",{className:"p-4 bg-white rounded",children:"Passenger not found"});async function T(){try{d.id?await Hc(d.id,d):await Bc(d),f("/admin/passengers")}catch(N){console.error("Failed to save passenger",N)}}async function y(){try{d.id&&await Np(d.id),f("/admin/passengers")}catch(N){console.error("Failed to delete passenger",N)}}return i.jsxs("div",{className:"bg-white p-4 rounded",children:[i.jsxs("div",{className:"flex justify-between items-start",children:[i.jsxs("div",{children:[i.jsx("h2",{className:"text-xl font-semibold",children:d.name}),i.jsxs("div",{className:"text-sm text-gray-600",children:["Seat: ",d.seat||"-"]})]}),i.jsxs("div",{className:"flex gap-2",children:[i.jsx("button",{onClick:()=>f("/admin/passengers"),className:"px-3 py-1 bg-gray-200 rounded",children:"Back"}),i.jsx("button",{onClick:T,className:"px-3 py-1 bg-yellow-400 rounded",children:"Save"}),i.jsx("button",{onClick:y,className:"px-3 py-1 bg-red-500 text-white rounded",children:"Delete"})]})]}),i.jsxs("div",{className:"mt-4 grid grid-cols-2 gap-3",children:[i.jsxs("div",{children:[i.jsx("div",{className:"font-medium",children:"Passport"}),i.jsx("input",{value:d.passportNumber||"",onChange:N=>r(z=>({...z,passportNumber:N.target.value})),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("div",{className:"font-medium",children:"Date of Birth"}),i.jsx("input",{type:"date",value:d.dateOfBirth||"",onChange:N=>r(z=>({...z,dateOfBirth:N.target.value})),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{className:"col-span-2",children:[i.jsx("div",{className:"font-medium",children:"Address"}),i.jsx("input",{value:d.address||"",onChange:N=>r(z=>({...z,address:N.target.value})),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("div",{className:"font-medium",children:"Phone"}),i.jsx("input",{value:d.phoneNumber||"",onChange:N=>r(z=>({...z,phoneNumber:N.target.value})),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("div",{className:"font-medium",children:"Seat"}),i.jsx("input",{value:d.seat||"",onChange:N=>r(z=>({...z,seat:N.target.value})),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{className:"col-span-2",children:[i.jsx("div",{className:"font-medium",children:"Ancillary Services"}),i.jsx("div",{className:"flex gap-2 mt-2",children:["Meal","Ancillary","Shopping"].map(N=>i.jsxs("label",{className:"inline-flex items-center gap-2",children:[i.jsx("input",{type:"checkbox",checked:(d.services||[]).includes(N),onChange:()=>r(z=>({...z,services:z.services&&z.services.includes(N)?z.services.filter(C=>C!==N):[...z.services||[],N]}))}),i.jsx("span",{children:N})]},N))})]})]}),i.jsxs("div",{className:"mt-6",children:[i.jsx("h3",{className:"font-semibold",children:"Travel history"}),i.jsx("div",{className:"overflow-x-auto bg-white rounded shadow mt-3",children:i.jsxs("table",{className:"w-full table-auto border-collapse",children:[i.jsx("thead",{children:i.jsxs("tr",{className:"text-left border-b",children:[i.jsx("th",{className:"p-2",children:"Date"}),i.jsx("th",{className:"p-2",children:"Flight"}),i.jsx("th",{className:"p-2",children:"Route"}),i.jsx("th",{className:"p-2",children:"Seat"}),i.jsx("th",{className:"p-2",children:"Booking Ref"}),i.jsx("th",{className:"p-2",children:"Status"}),i.jsx("th",{className:"p-2",children:"Notes"})]})}),i.jsxs("tbody",{children:[S.map(N=>{const z=g.get(N.flightId);return i.jsxs("tr",{className:"border-b",children:[i.jsx("td",{className:"p-2",children:N.date}),i.jsx("td",{className:"p-2",children:z?z.name:"Unknown"}),i.jsxs("td",{className:"p-2",children:[N.origin," 192 ",N.destination]}),i.jsx("td",{className:"p-2",children:N.seat||"-"}),i.jsx("td",{className:"p-2",children:N.bookingReference}),i.jsx("td",{className:"p-2",children:N.status}),i.jsx("td",{className:"p-2",children:N.notes||"-"})]},N.id)}),S.length===0&&i.jsx("tr",{children:i.jsx("td",{colSpan:7,className:"p-4 text-sm text-gray-600",children:"No travel history for this passenger."})})]})]})})]})]})}const Dp="http://localhost:8090";async function $l(s,f={}){const d={"Content-Type":"application/json",...f.headers||{}},r=Xl.getToken();r&&(d.Authorization=`Bearer ${r}`);const h=await fetch(`${Dp}${s}`,{...f,headers:d});if(!h.ok){const S=await h.text();throw new Error(S||h.statusText)}const p=await h.text();return p?JSON.parse(p):null}function Ha(s){return s&&{id:s.userId||s.id,username:s.username||s.userName||s.login||null,name:s.name||s.fullName||`${s.firstName||""} ${s.lastName||""}`.trim(),role:s.role||s.roles||null,email:s.email||s.contactEmail||null,phoneNumber:s.phoneNumber||s.phone||null,flightId:s.flightId||s.assignedFlightId||null,staff:s.staff||s.isStaff||!1,enabled:s.enabled!==void 0?s.enabled:s.active!==void 0?s.active:!0,raw:s}}async function Cp(){const s=await $l("/users",{method:"GET"});return Array.isArray(s)?s.map(Ha):[]}async function _p(s){const f=await $l("/users",{method:"POST",body:JSON.stringify(s)});return f&&Ha(f)}async function Up(s,f){const d=await $l(`/users/${s}`,{method:"PUT",body:JSON.stringify(f)});return d&&Ha(d)}async function Bp(s){return $l(`/users/${s}`,{method:"DELETE"})}async function Hp(s){const f=await $l(`/users/username/${encodeURIComponent(s)}`,{method:"GET"});return f?Ha(f):null}async function qp(s){const f=await $l(`/users/role/${encodeURIComponent(s)}`,{method:"GET"});return Array.isArray(f)?f.map(Ha):[]}async function wp(){const s=await $l("/users/staff",{method:"GET"});return Array.isArray(s)?s.map(Ha):[]}function Pd(){const{id:s}=Vl(),f=$e(),[d,r]=O.useState(null),[h,p]=O.useState([]);O.useEffect(()=>{let X=!0;return s&&s!=="new"&&Jl(s).then(F=>{X&&r(F)}).catch(()=>{X&&r(null)}),()=>{X=!1}},[s]);function S(X){if(!X)return"";const F=String(X).trim(),je=F.match(/^(\d{1,2}):(\d{2})\s*([AaPp][Mm])$/);if(je){let Ae=Number(je[1]);const U=je[2],Z=je[3].toUpperCase();return Z==="PM"&&Ae!==12&&(Ae+=12),Z==="AM"&&Ae===12&&(Ae=0),`${String(Ae).padStart(2,"0")}:${U}`}const we=F.match(/^(\d{1,2}):(\d{2})$/);return we?`${String(Number(we[1])).padStart(2,"0")}:${we[2]}`:""}const _=d?{...d,departureTime:S(d.departureTime),arrivalTime:S(d.arrivalTime),inflightStaff:d&&d.inflightStaff?Array.isArray(d.inflightStaff)?d.inflightStaff:[d.inflightStaff]:[]}:{name:"",route:"",date:"",departureTime:"",arrivalTime:"",totalSeats:"",inflightStaff:[],services:[]},[g,m]=O.useState(_);O.useEffect(()=>{m(d?{...d,departureTime:S(d.departureTime),arrivalTime:S(d.arrivalTime),inflightStaff:d&&d.inflightStaff?Array.isArray(d.inflightStaff)?d.inflightStaff:[d.inflightStaff]:[]}:{name:"",route:"",date:"",departureTime:"",arrivalTime:"",totalSeats:"",inflightStaff:[],services:[]})},[d]),O.useEffect(()=>{let X=!0;async function F(){try{const je=await Kl();if(!X)return;p(je||[])}catch(je){console.error("Failed to load flights",je),X&&p([])}}return F(),()=>{X=!1}},[]);async function T(){try{g.id?await pp(g.id,g):await vp(g),f("/admin/flights")}catch(X){console.error("Save failed",X),f("/admin/flights")}}async function y(){g.id&&await yp(g.id),f("/admin/flights")}function N(X){m(F=>({...F,services:F.services&&F.services.includes(X)?F.services.filter(je=>je!==X):[...F.services||[],X]}))}const[z,C]=O.useState(""),[G,M]=O.useState("Veg"),[L,K]=O.useState(""),[Q,W]=O.useState(""),[w,fe]=O.useState(""),[Ce,Fe]=O.useState([]);O.useEffect(()=>{let X=!0;async function F(){try{const je=await wp();if(!X)return;Fe(je||[])}catch(je){console.error("Failed to load users",je)}}return F(),()=>{X=!1}},[]);function ht(){if(!z)return;let X=z;if(z==="Ancillary"){if(!L)return;X=`Ancillary: ${L} ($${Q||0})`}else if(z==="Meal"){if(!L)return;X=`Meal (${G}): ${L} ($${Q||0})`}else if(z==="Shopping"){if(!L)return;X=`Shopping: ${L} ($${Q||0})`}m(F=>({...F,services:[...F.services||[],X]})),C(""),M("Veg"),K(""),W("")}function $t(X){m(F=>({...F,services:(F.services||[]).filter((je,we)=>we!==X)}))}return i.jsxs("div",{className:"bg-white p-4 rounded",children:[i.jsxs("div",{className:"flex justify-between items-start",children:[i.jsxs("div",{children:[i.jsx("h2",{className:"text-xl font-semibold",children:g.name||"New Flight"}),i.jsxs("div",{className:"text-sm text-gray-600",children:["Route: ",g.route||"-"]})]}),i.jsxs("div",{className:"flex gap-2",children:[i.jsx("button",{onClick:()=>f("/admin/flights"),className:"px-3 py-1 bg-gray-200 rounded",children:"Back"}),i.jsx("button",{onClick:T,className:"px-3 py-1 bg-yellow-400 rounded",children:"Save"}),g.id&&i.jsx("button",{onClick:y,className:"px-3 py-1 bg-red-500 text-white rounded",children:"Delete"})]})]}),i.jsxs("div",{className:"mt-4 grid grid-cols-2 gap-3",children:[i.jsxs("div",{children:[i.jsx("label",{className:"block",children:"Name"}),i.jsx("input",{value:g.name,onChange:X=>m(F=>({...F,name:X.target.value})),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block",children:"Route"}),i.jsxs("select",{value:g.route,onChange:X=>m(F=>({...F,route:X.target.value})),className:"w-full border p-2 rounded",children:[i.jsx("option",{value:"",children:"Select route"}),Array.from(new Set(h.map(X=>X.route).filter(Boolean))).map(X=>i.jsx("option",{value:X,children:X},X))]})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block",children:"Date"}),i.jsx("input",{type:"date",value:g.date||"",onChange:X=>m(F=>({...F,date:X.target.value})),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block",children:"Departure Time"}),i.jsx("input",{type:"time",value:g.departureTime||"",onChange:X=>m(F=>({...F,departureTime:X.target.value})),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block",children:"Arrival Time"}),i.jsx("input",{type:"time",value:g.arrivalTime||"",onChange:X=>m(F=>({...F,arrivalTime:X.target.value})),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block",children:"Number of seats"}),i.jsx("input",{type:"number",min:"0",value:g.totalSeats||"",onChange:X=>m(F=>({...F,totalSeats:X.target.value})),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block",children:"Assign Inflight Staff"}),i.jsxs("div",{className:"flex gap-2 items-center",children:[i.jsxs("select",{value:w,onChange:X=>fe(X.target.value),className:"w-full border p-2 rounded",children:[i.jsx("option",{value:"",children:"Select staff to add"}),Ce.filter(X=>X.role==="inflightStaff").map(X=>i.jsx("option",{value:X.username,children:X.name},X.username))]}),i.jsx("button",{type:"button",onClick:()=>{w&&((g.inflightStaff||[]).includes(w)||(m(X=>({...X,inflightStaff:[...X.inflightStaff||[],w]})),fe("")))},className:"px-3 py-1 bg-blue-500 text-white rounded",children:"Add"})]}),i.jsx("div",{className:"mt-2 space-y-2",children:(g.inflightStaff||[]).map((X,F)=>{const je=Ce.find(we=>we.username===X);return i.jsxs("div",{className:"flex items-center justify-between bg-gray-50 p-2 rounded",children:[i.jsx("div",{children:je?je.name:X}),i.jsx("button",{type:"button",onClick:()=>m(we=>({...we,inflightStaff:(we.inflightStaff||[]).filter((Ae,U)=>U!==F)})),className:"text-red-500",children:"Remove"})]},X)})})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block",children:"Services"}),i.jsx("div",{className:"flex gap-2 mt-2",children:["Ancillary","Meal","Shopping"].map(X=>i.jsxs("label",{className:"inline-flex items-center gap-2",children:[i.jsx("input",{type:"checkbox",checked:(g.services||[]).includes(X),onChange:()=>N(X)}),i.jsx("span",{children:X})]},X))}),i.jsxs("div",{className:"mt-4 border-t pt-3",children:[i.jsx("label",{className:"block font-medium",children:"Add service"}),i.jsxs("div",{className:"flex gap-2 items-center mt-2",children:[i.jsxs("select",{value:z,onChange:X=>C(X.target.value),className:"border p-2 rounded",children:[i.jsx("option",{value:"",children:"Select service"}),i.jsx("option",{value:"Ancillary",children:"Ancillary"}),i.jsx("option",{value:"Meal",children:"Meal"}),i.jsx("option",{value:"Shopping",children:"Shopping"})]}),z==="Ancillary"&&i.jsxs(i.Fragment,{children:[i.jsx("input",{placeholder:"Service name",value:L,onChange:X=>K(X.target.value),className:"border p-2 rounded"}),i.jsx("input",{placeholder:"Price",value:Q,onChange:X=>W(X.target.value),className:"border p-2 rounded w-28"})]}),z==="Meal"&&i.jsxs(i.Fragment,{children:[i.jsxs("select",{value:G,onChange:X=>M(X.target.value),className:"border p-2 rounded",children:[i.jsx("option",{value:"Veg",children:"Veg"}),i.jsx("option",{value:"Non-Veg",children:"Non Veg"})]}),i.jsx("input",{placeholder:"Dish name",value:L,onChange:X=>K(X.target.value),className:"border p-2 rounded"}),i.jsx("input",{placeholder:"Price",value:Q,onChange:X=>W(X.target.value),className:"border p-2 rounded w-28"})]}),z==="Shopping"&&i.jsxs(i.Fragment,{children:[i.jsx("input",{placeholder:"Product name",value:L,onChange:X=>K(X.target.value),className:"border p-2 rounded"}),i.jsx("input",{placeholder:"Price",value:Q,onChange:X=>W(X.target.value),className:"border p-2 rounded w-28"})]}),i.jsx("button",{type:"button",onClick:ht,className:"px-3 py-1 bg-blue-500 text-white rounded",children:"Add"})]})]}),i.jsx("div",{className:"mt-3",children:(g.services||[]).map((X,F)=>["Ancillary","Meal","Shopping"].includes(X)?null:i.jsxs("div",{className:"flex items-center justify-between bg-gray-50 p-2 rounded mb-2",children:[i.jsx("div",{className:"text-sm",children:X}),i.jsx("button",{type:"button",onClick:()=>$t(F),className:"text-red-500",children:"Remove"})]},F))})]})]})]})}function Yp(){const[s,f]=O.useState(""),[d,r]=O.useState([]),[h,p]=O.useState(!0);O.useEffect(()=>{let z=!0;async function C(){p(!0);try{const G=await Op();if(!z)return;r(Array.isArray(G)?G:[])}catch(G){console.error("Failed to load travel history",G),z&&r([])}finally{z&&p(!1)}}return C(),()=>{z=!1}},[]);const[S,_]=O.useState(new Map),[g,m]=O.useState(new Map);O.useEffect(()=>{let z=!0;async function C(){try{const[G,M]=await Promise.all([ph(),Kl()]);if(!z)return;_(new Map((G||[]).map(L=>[Number(L.id),L]))),m(new Map((M||[]).map(L=>[Number(L.id),L])))}catch(G){console.error("Failed to load passenger/flight maps",G)}}return C(),()=>{z=!1}},[]);function T(z){const C=S.get(Number(z));return C?C.name:"Unknown"}function y(z){const C=g.get(Number(z));return C?`${C.name} (${C.route})`:"Unknown"}const N=d.filter(z=>{if(!s)return!0;const C=s.toLowerCase();return(T(z.passengerId)||"").toLowerCase().includes(C)||(y(z.flightId)||"").toLowerCase().includes(C)||(z.bookingReference||"").toLowerCase().includes(C)||(z.origin||"").toLowerCase().includes(C)||(z.destination||"").toLowerCase().includes(C)});return i.jsxs("div",{children:[i.jsxs("div",{className:"flex items-center justify-between mb-4",children:[i.jsx("h2",{className:"text-xl font-semibold",children:"Travel history"}),i.jsx("input",{value:s,onChange:z=>f(z.target.value),placeholder:"Search (passenger, flight, booking ref, origin/dest)",className:"border p-2 rounded w-80"})]}),i.jsx("div",{className:"overflow-x-auto bg-white rounded shadow",children:i.jsxs("table",{className:"w-full table-auto border-collapse",children:[i.jsx("thead",{children:i.jsxs("tr",{className:"text-left border-b",children:[i.jsx("th",{className:"p-2",children:"Date"}),i.jsx("th",{className:"p-2",children:"Passenger"}),i.jsx("th",{className:"p-2",children:"Flight"}),i.jsx("th",{className:"p-2",children:"Route"}),i.jsx("th",{className:"p-2",children:"Seat"}),i.jsx("th",{className:"p-2",children:"Booking Ref"}),i.jsx("th",{className:"p-2",children:"Class"}),i.jsx("th",{className:"p-2",children:"Status"}),i.jsx("th",{className:"p-2",children:"Distance (km)"}),i.jsx("th",{className:"p-2",children:"Duration (min)"}),i.jsx("th",{className:"p-2",children:"Notes"})]})}),i.jsxs("tbody",{children:[h?i.jsx("div",{className:"p-4",children:"Loading..."}):N.map(z=>i.jsxs("tr",{className:"border-b",children:[i.jsx("td",{className:"p-2",children:z.date}),i.jsx("td",{className:"p-2",children:T(z.passengerId)}),i.jsx("td",{className:"p-2",children:y(z.flightId)}),i.jsxs("td",{className:"p-2",children:[z.origin," → ",z.destination]}),i.jsx("td",{className:"p-2",children:z.seat||"-"}),i.jsx("td",{className:"p-2",children:z.bookingReference}),i.jsx("td",{className:"p-2",children:z.fareClass}),i.jsx("td",{className:"p-2",children:z.status}),i.jsx("td",{className:"p-2",children:z.distanceKm??"-"}),i.jsx("td",{className:"p-2",children:z.durationMin??"-"}),i.jsx("td",{className:"p-2",children:z.notes||"-"})]},z.id)),N.length===0&&i.jsx("tr",{children:i.jsx("td",{colSpan:11,className:"p-4 text-sm text-gray-600",children:"No records match your search."})})]})]})})]})}function Gp(){const[s,f]=O.useState([]),[d,r]=O.useState("All"),[h,p]=O.useState(!0);async function S(){p(!0);try{const g=await Cp();f(g)}catch(g){console.error("Failed to load users",g),f([])}finally{p(!1)}}O.useEffect(()=>{S()},[]);const _=d==="All"?s:s.filter(g=>g.role===d);return i.jsxs("div",{children:[i.jsxs("div",{className:"flex items-center justify-between mb-4",children:[i.jsx("h2",{className:"text-xl font-semibold",children:"Users"}),i.jsx("div",{children:i.jsx(Ll,{to:"/admin/users/new",className:"px-3 py-1 bg-green-600 text-white rounded",children:"Add User"})})]}),i.jsxs("div",{className:"mb-4 flex gap-3 items-center",children:[i.jsx("label",{children:"Role:"}),i.jsxs("select",{value:d,onChange:g=>r(g.target.value),className:"border p-2 rounded",children:[i.jsx("option",{children:"All"}),i.jsx("option",{children:"admin"}),i.jsx("option",{children:"staff"}),i.jsx("option",{children:"user"})]}),i.jsx("button",{onClick:S,className:"px-3 py-1 bg-indigo-600 text-white rounded",children:"Refresh"})]}),h?i.jsx("div",{children:"Loading..."}):i.jsx("div",{className:"bg-white rounded shadow overflow-hidden",children:i.jsxs("table",{className:"w-full table-auto",children:[i.jsx("thead",{children:i.jsxs("tr",{className:"text-left border-b",children:[i.jsx("th",{className:"p-2",children:"Username"}),i.jsx("th",{className:"p-2",children:"Name"}),i.jsx("th",{className:"p-2",children:"Role"}),i.jsx("th",{className:"p-2",children:"Email"}),i.jsx("th",{className:"p-2",children:"Actions"})]})}),i.jsxs("tbody",{children:[_.map(g=>i.jsxs("tr",{className:"border-b",children:[i.jsx("td",{className:"p-2",children:i.jsx(Ll,{to:`/admin/users/${g.username}`,children:g.username})}),i.jsx("td",{className:"p-2",children:g.name}),i.jsx("td",{className:"p-2",children:Array.isArray(g.role)?g.role.join(", "):g.role}),i.jsx("td",{className:"p-2",children:g.email}),i.jsx("td",{className:"p-2",children:i.jsx(Ll,{to:`/admin/users/${g.username}`,className:"text-indigo-600",children:"Edit"})})]},g.id)),_.length===0&&i.jsx("tr",{children:i.jsx("td",{colSpan:5,className:"p-4 text-sm text-gray-600",children:"No users found."})})]})]})})]})}function Id(){const{username:s}=Vl(),f=$e(),[d,r]=O.useState({username:"",name:"",role:"user",email:"",phoneNumber:""}),[h,p]=O.useState(!1);O.useEffect(()=>{if(!s||s==="new")return;let g=!0;return p(!0),Hp(s).then(m=>{g&&r(m||d)}).catch(m=>console.error(m)).finally(()=>{g&&p(!1)}),()=>{g=!1}},[s]);async function S(){try{d.id?await Up(d.id,d):await _p(d),f("/admin/users")}catch(g){console.error("Failed to save user",g)}}async function _(){try{d.id&&await Bp(d.id),f("/admin/users")}catch(g){console.error("Failed to delete user",g)}}return h?i.jsx("div",{children:"Loading..."}):i.jsxs("div",{className:"bg-white p-4 rounded",children:[i.jsxs("div",{className:"flex justify-between items-center mb-4",children:[i.jsx("h2",{className:"text-xl font-semibold",children:d.id?"Edit User":"New User"}),i.jsxs("div",{className:"flex gap-2",children:[i.jsx("button",{onClick:()=>f("/admin/users"),className:"px-3 py-1 bg-gray-200 rounded",children:"Back"}),i.jsx("button",{onClick:S,className:"px-3 py-1 bg-yellow-400 rounded",children:"Save"}),d.id&&i.jsx("button",{onClick:_,className:"px-3 py-1 bg-red-500 text-white rounded",children:"Delete"})]})]}),i.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[i.jsxs("div",{children:[i.jsx("div",{className:"font-medium",children:"Username"}),i.jsx("input",{value:d.username||"",onChange:g=>r(m=>({...m,username:g.target.value})),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("div",{className:"font-medium",children:"Name"}),i.jsx("input",{value:d.name||"",onChange:g=>r(m=>({...m,name:g.target.value})),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("div",{className:"font-medium",children:"Role"}),i.jsxs("select",{value:d.role||"user",onChange:g=>r(m=>({...m,role:g.target.value})),className:"w-full border p-2 rounded",children:[i.jsx("option",{value:"user",children:"user"}),i.jsx("option",{value:"staff",children:"staff"}),i.jsx("option",{value:"admin",children:"admin"})]})]}),i.jsxs("div",{children:[i.jsx("div",{className:"font-medium",children:"Email"}),i.jsx("input",{value:d.email||"",onChange:g=>r(m=>({...m,email:g.target.value})),className:"w-full border p-2 rounded"})]}),i.jsxs("div",{children:[i.jsx("div",{className:"font-medium",children:"Phone"}),i.jsx("input",{value:d.phoneNumber||"",onChange:g=>r(m=>({...m,phoneNumber:g.target.value})),className:"w-full border p-2 rounded"})]})]})]})}function Lp(){const{state:s}=At(),f=$e(),d=s?.userType||s?.role||"staff";return O.useEffect(()=>{d==="admin"?f("/admin",{state:s}):d==="inflightStaff"?f("/inflight-service",{state:s}):d==="checkinStaff"?f("/staff/check-in",{state:s}):f("/")},[d,f]),null}function Xp({locationState:s}){const f=$e(),[d,r]=O.useState(""),[h,p]=O.useState(""),[S,_]=O.useState(""),[g,m]=O.useState([]);function T(y){y.preventDefault();const N=`${d.toUpperCase()}-${h.toUpperCase()}`,z=(bh||[]).filter(C=>C.route===N&&(!S||C.date===S));m(z)}return i.jsxs("div",{className:"bg-white p-6 rounded shadow",children:[i.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Passenger Dashboard"}),i.jsxs("form",{onSubmit:T,className:"grid grid-cols-4 gap-4 items-end mb-4",children:[i.jsxs("label",{children:[i.jsx("div",{className:"text-sm text-gray-600",children:"From"}),i.jsx("input",{value:d,onChange:y=>r(y.target.value),className:"mt-1 rounded border-gray-200",placeholder:"e.g. NYC"})]}),i.jsxs("label",{children:[i.jsx("div",{className:"text-sm text-gray-600",children:"To"}),i.jsx("input",{value:h,onChange:y=>p(y.target.value),className:"mt-1 rounded border-gray-200",placeholder:"e.g. LON"})]}),i.jsxs("label",{children:[i.jsx("div",{className:"text-sm text-gray-600",children:"Departure Date"}),i.jsx("input",{type:"date",value:S,onChange:y=>_(y.target.value),className:"mt-1 rounded border-gray-200"})]}),i.jsx("div",{children:i.jsx("button",{type:"submit",className:"bg-indigo-600 text-white py-2 px-4 rounded",children:"Search"})})]}),g.length>0?i.jsx("div",{className:"overflow-x-auto",children:i.jsxs("table",{className:"min-w-full bg-white",children:[i.jsx("thead",{children:i.jsxs("tr",{children:[i.jsx("th",{className:"text-left p-2",children:"Flight"}),i.jsx("th",{className:"text-left p-2",children:"Departure"}),i.jsx("th",{className:"text-left p-2",children:"Arrival"}),i.jsx("th",{className:"text-left p-2",children:"Duration"}),i.jsx("th",{className:"text-left p-2",children:"Available Seats"}),i.jsx("th",{className:"text-left p-2",children:"Action"})]})}),i.jsx("tbody",{children:g.map(y=>i.jsxs("tr",{className:"border-t",children:[i.jsxs("td",{className:"p-2",children:[y.name," (",y.route,")"]}),i.jsxs("td",{className:"p-2",children:[y.date," ",y.departureTime]}),i.jsxs("td",{className:"p-2",children:[y.date," ",y.arrivalTime]}),i.jsx("td",{className:"p-2",children:Qp(y.departureTime,y.arrivalTime)}),i.jsx("td",{className:"p-2",children:y.availableSeats}),i.jsx("td",{className:"p-2",children:i.jsx("button",{onClick:()=>f("/passenger/book",{state:{flightId:y.id}}),className:"bg-green-600 text-white py-1 px-3 rounded",disabled:y.availableSeats<=0,children:"Book"})})]},y.id))})]})}):i.jsx("div",{className:"text-sm text-gray-500",children:"No results. Enter a route and click Search."})]})}let bh=null;(async function(){try{bh=await Kl()}catch(f){console.error("Failed to preload flights",f)}})();function Qp(s,f){try{const d=g=>{const[m,T]=g.split(" "),[y,N]=m.split(":").map(Number);let z=y%12;return T==="PM"&&(z+=12),z*60+N},r=d(s);let p=d(f)-r;p<0&&(p+=1440);const S=Math.floor(p/60),_=p%60;return`${S}h ${_}m`}catch{return"-"}}function Zp(){const{state:s}=At(),f=$e(),d=s?.flightId,[r,h]=O.useState(null),[p,S]=O.useState(null),[_,g]=O.useState({name:"",email:"",phoneNumber:"",passportNumber:"",dateOfBirth:""});if((async()=>{try{if(d&&!r&&h(await Jl(d)),!p){const y=await qp("passenger");y&&y.length>0&&S(y[0])}}catch(y){console.error(y)}})(),!r)return i.jsx("div",{className:"bg-white p-6 rounded",children:"Flight not found."});function m(y){g({..._,[y.target.name]:y.target.value})}async function T(){try{const y={flightId:r.id,name:_.name||p?.name,phoneNumber:_.phoneNumber||p?.phoneNumber,address:"",passportNumber:_.passportNumber,dateOfBirth:_.dateOfBirth,from:r.route.split("-")[0],to:r.route.split("-")[1],services:[],seat:null,checkedIn:!1,wheelchair:!1,infant:!1},N=await Bc(y);f("/passenger/confirm",{state:{passenger:N,flight:r}})}catch(y){console.error("Failed to create passenger",y)}}return i.jsxs("div",{className:"bg-white p-6 rounded shadow",children:[i.jsxs("h2",{className:"text-xl font-semibold mb-4",children:["Booking: ",r.name," (",r.route,")"]}),i.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[i.jsxs("div",{children:[i.jsx("h3",{className:"font-medium",children:"Flight details"}),i.jsxs("p",{className:"text-sm text-gray-600",children:[r.date," · ",r.departureTime," → ",r.arrivalTime]}),i.jsxs("p",{className:"text-sm",children:["Aircraft: ",r.aircraftType]}),i.jsxs("p",{className:"text-sm",children:["Available seats: ",r.availableSeats]})]}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-medium",children:"Passenger details"}),i.jsxs("label",{className:"block text-sm mt-2",children:[i.jsx("div",{className:"text-xs text-gray-500",children:"Name"}),i.jsx("input",{name:"name",value:_.name,onChange:m,className:"mt-1 rounded border-gray-200"})]}),i.jsxs("label",{className:"block text-sm mt-2",children:[i.jsx("div",{className:"text-xs text-gray-500",children:"Email"}),i.jsx("input",{name:"email",value:_.email,onChange:m,className:"mt-1 rounded border-gray-200"})]}),i.jsxs("label",{className:"block text-sm mt-2",children:[i.jsx("div",{className:"text-xs text-gray-500",children:"Phone"}),i.jsx("input",{name:"phoneNumber",value:_.phoneNumber,onChange:m,className:"mt-1 rounded border-gray-200"})]}),i.jsxs("label",{className:"block text-sm mt-2",children:[i.jsx("div",{className:"text-xs text-gray-500",children:"Passport"}),i.jsx("input",{name:"passportNumber",value:_.passportNumber,onChange:m,className:"mt-1 rounded border-gray-200"})]}),i.jsxs("label",{className:"block text-sm mt-2",children:[i.jsx("div",{className:"text-xs text-gray-500",children:"Date of birth"}),i.jsx("input",{type:"date",name:"dateOfBirth",value:_.dateOfBirth,onChange:m,className:"mt-1 rounded border-gray-200"})]})]})]}),i.jsxs("div",{className:"mt-4 flex gap-2",children:[i.jsx("button",{onClick:T,className:"bg-indigo-600 text-white py-2 px-4 rounded",children:"Confirm Booking"}),i.jsx("button",{onClick:()=>f(-1),className:"py-2 px-4 rounded border",children:"Back"})]})]})}function Vp(){const{state:s}=At(),f=$e(),d=s?.passenger,r=s?.flight;return!d||!r?i.jsx("div",{className:"bg-white p-6 rounded",children:"No confirmation data."}):i.jsxs("div",{className:"bg-white p-6 rounded shadow",children:[i.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Booking Confirmed"}),i.jsx("p",{className:"text-sm",children:"Congratulations, your booking is confirmed."}),i.jsxs("div",{className:"mt-4",children:[i.jsx("h3",{className:"font-medium",children:"Passenger"}),i.jsx("p",{children:d.name}),i.jsx("p",{children:d.phoneNumber})]}),i.jsxs("div",{className:"mt-4",children:[i.jsx("h3",{className:"font-medium",children:"Flight"}),i.jsxs("p",{children:[r.name," — ",r.route]}),i.jsxs("p",{children:[r.date," · ",r.departureTime," → ",r.arrivalTime]})]}),i.jsx("div",{className:"mt-6",children:i.jsx("button",{onClick:()=>f("/passenger"),className:"bg-indigo-600 text-white py-2 px-4 rounded",children:"Back to Dashboard"})})]})}function Kp(){const s=$e(),{state:f}=At(),[d,r]=O.useState(""),[h,p]=O.useState([]),[S,_]=O.useState(null),[g,m]=O.useState({}),[T,y]=O.useState(null),N=f?.flightId||null;O.useEffect(()=>{let M=!0;async function L(){if(N)try{const[K,Q]=await Promise.all([qc(Number(N)),Jl(Number(N))]);if(!M)return;const W=K.map(w=>({...w,shoppingRequests:w.shoppingRequests||w.shoppingItems||[],ancillaryServices:w.ancillaryServices||w.ancillary||w.services||[],specialMeal:w.specialMeal||w.mealName||"",mealType:w.mealType||w.mealPreference||"",seat:w.seat||""}));p(W),y(Q)}catch(K){console.error("Failed to load inflight data",K)}}return L(),()=>{M=!1}},[N]);const z=new Map;h.forEach(M=>{const L=parseInt((M.seat||"").toString().match(/^\d+/)?.[0],10);isNaN(L)||(z.has(L)||z.set(L,[]),z.get(L).push(M))});const C=()=>{s("/staff")},G=h.filter(M=>(M.name||"").toLowerCase().includes(d.toLowerCase())||(M.seat||"").toLowerCase().includes(d.toLowerCase()));return i.jsxs("div",{className:"min-h-screen flex flex-col items-center justify-center bg-slate-50 p-6",children:[i.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[i.jsx("button",{onClick:C,className:"text-indigo-600 hover:text-indigo-800",children:"←"}),i.jsx("h1",{className:"text-2xl font-semibold",children:"Inflight Service Page"})]}),i.jsxs("div",{className:"p-6 w-full",children:[T&&i.jsxs("div",{className:"mb-6",children:[i.jsxs("h2",{className:"text-lg font-semibold mb-2",children:["Seat Map (",T.name,")"]}),i.jsx("div",{className:"grid grid-cols-6 gap-2",children:T.seatMap.map(M=>{const L=z.get(M.number)||[],K=L[0];let Q="";if(L.length>0&&K){const fe=(K.mealType||"").toString().toLowerCase();fe.includes("veg")?Q="bg-green-400 text-white":fe.includes("non")?Q="bg-red-400 text-white":Q="bg-yellow-200"}else M.isBooked?Q="bg-gray-200":Q="bg-white";const W=L.length>0?`${L.map(fe=>fe.name).join(", ")} - ${L.map(fe=>fe.mealType||fe.specialMeal||"Unknown").join(", ")}`:M.isBooked?"Booked (no passenger data)":"Available",w=L.length>0?K.mealType||K.specialMeal||"Unknown":M.isBooked?"Booked":"Free";return i.jsxs("div",{title:W,className:`border border-gray-300 px-2 py-3 rounded text-center text-sm ${Q}`,children:[i.jsx("div",{className:"font-semibold",children:M.number}),i.jsx("div",{className:"text-xs",children:w})]},M.number)})})]}),i.jsx("div",{className:"mb-4",children:i.jsx("input",{type:"text",placeholder:"Search by passenger name or seat number",value:d,onChange:M=>r(M.target.value),className:"px-4 py-2 border rounded w-full"})}),i.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Passenger List"}),i.jsxs("table",{className:"table-auto w-full border-collapse border border-gray-300",children:[i.jsx("thead",{children:i.jsxs("tr",{children:[i.jsx("th",{className:"border border-gray-300 px-4 py-2",children:"Passenger Name"}),i.jsx("th",{className:"border border-gray-300 px-4 py-2",children:"Seat"}),i.jsx("th",{className:"border border-gray-300 px-4 py-2",children:"Special Meal"}),i.jsx("th",{className:"border border-gray-300 px-4 py-2",children:"Ancillary Services"}),i.jsx("th",{className:"border border-gray-300 px-4 py-2",children:"Shopping Requests"}),i.jsx("th",{className:"border border-gray-300 px-4 py-2",children:"Services"})]})}),i.jsx("tbody",{children:G.map(M=>i.jsxs("tr",{className:M.specialMeal?"bg-yellow-100":"",children:[i.jsx("td",{className:"border border-gray-300 px-4 py-2",children:i.jsx("span",{className:"text-indigo-600 cursor-pointer hover:underline",onClick:()=>s(`/inflight-service/passenger/${M.id}`,{state:{passenger:M,flightId:M.flightId}}),children:M.name})}),i.jsx("td",{className:"border border-gray-300 px-4 py-2",children:M.seat}),i.jsx("td",{className:"border border-gray-300 px-4 py-2",children:M.specialMeal||"None"}),i.jsx("td",{className:"border border-gray-300 px-4 py-2",children:M.ancillaryServices&&M.ancillaryServices.length>0?M.ancillaryServices.join(", "):"None"}),i.jsx("td",{className:"border border-gray-300 px-4 py-2",children:M.shoppingRequests&&M.shoppingRequests.length>0?M.shoppingRequests.join(", "):"None"}),i.jsx("td",{className:"border border-gray-300 px-4 py-2",children:i.jsxs("div",{className:"flex flex-col gap-2",children:[i.jsx("div",{className:"text-sm",children:M.ancillaryServices&&M.ancillaryServices.length>0?M.ancillaryServices.join(", "):"No ancillaries"}),i.jsx("div",{className:"text-sm",children:M.specialMeal||"No meal"}),i.jsx("div",{className:"text-sm",children:M.shoppingRequests&&M.shoppingRequests.length>0?M.shoppingRequests.join(", "):"No shopping"})]})})]},M.id))})]})]})]})}function Jp({passenger:s,flight:f,onClose:d,onUpdate:r}){const[h,p]=O.useState({}),[S,_]=O.useState([]);if(O.useEffect(()=>{if(!s||!f)return;const y=f.services||[];_(y);const N={};s.specialMeal&&(N.mealName=s.specialMeal),s.ancillaryServices&&s.ancillaryServices.length&&(N.ancillaryItem=s.ancillaryServices[0]),s.shoppingRequests&&s.shoppingRequests.length&&(N.shoppingItems=s.shoppingRequests),p(N)},[s,f]),!s)return null;const g=y=>{let N={...s};if(y==="Meal"){const z=h.mealName||window.prompt("Enter meal name:")||"N/A";N={...N,specialMeal:z}}else if(y==="Ancillary"){const z=h.ancillaryItem||h.extraBaggage||window.prompt("Enter ancillary item:");if(!z)return;N={...N,ancillaryServices:[...N.ancillaryServices||[],z]}}else if(y==="Shopping"){let z=[];Array.isArray(h.shoppingItems)?z=h.shoppingItems:typeof h.shoppingItems=="string"&&h.shoppingItems.length?z=h.shoppingItems.split(","):z=window.prompt("Enter shopping items (comma-separated)")?.split(",")||[],N={...N,shoppingRequests:[...N.shoppingRequests||[],...z.map(C=>C.trim()).filter(Boolean)]}}r&&r(N),p({})},m=(y,N)=>{let z={...s};y==="Meal"?z.specialMeal=null:y==="Ancillary"?z.ancillaryServices=(z.ancillaryServices||[]).filter(C=>C!==N):y==="Shopping"&&(z.shoppingRequests=(z.shoppingRequests||[]).filter(C=>C!==N)),r&&r(z)},T=(y,N)=>{y==="Meal"&&p({...h,mealName:N}),y==="Ancillary"&&p({...h,ancillaryItem:N}),y==="Shopping"&&p({...h,shoppingItems:[N]})};return i.jsxs("div",{className:"mt-6 p-4 border rounded bg-white",children:[i.jsxs("div",{className:"flex justify-between items-start",children:[i.jsxs("h3",{className:"text-lg font-semibold",children:["Passenger Details — ",s.name]}),i.jsx("div",{children:i.jsx("button",{onClick:d,className:"px-3 py-1 border rounded",children:"Close"})})]}),i.jsxs("div",{className:"mt-3 grid grid-cols-1 md:grid-cols-2 gap-4",children:[i.jsxs("div",{children:[i.jsxs("p",{children:[i.jsx("strong",{children:"Phone:"})," ",s.phoneNumber||"N/A"]}),i.jsxs("p",{children:[i.jsx("strong",{children:"Seat:"})," ",s.seat||"N/A"]}),i.jsxs("p",{children:[i.jsx("strong",{children:"From:"})," ",s.from||"N/A"]}),i.jsxs("p",{children:[i.jsx("strong",{children:"To:"})," ",s.to||"N/A"]})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"font-medium",children:"Services Availed"}),i.jsxs("ul",{className:"list-disc pl-6",children:[(s.ancillaryServices||[]).map((y,N)=>i.jsxs("li",{className:"flex items-center gap-2",children:["Ancillary: ",y,i.jsx("button",{onClick:()=>T("Ancillary",y),className:"ml-2 text-sm text-indigo-600",children:"Edit"}),i.jsx("button",{onClick:()=>m("Ancillary",y),className:"ml-2 text-sm text-red-600",children:"Delete"})]},`a${N}`)),s.specialMeal&&i.jsxs("li",{className:"flex items-center gap-2",children:["Meal: ",s.specialMeal,i.jsx("button",{onClick:()=>T("Meal",s.specialMeal),className:"ml-2 text-sm text-indigo-600",children:"Edit"}),i.jsx("button",{onClick:()=>m("Meal"),className:"ml-2 text-sm text-red-600",children:"Delete"})]}),(s.shoppingRequests||[]).map((y,N)=>i.jsxs("li",{className:"flex items-center gap-2",children:["Shopping: ",y,i.jsx("button",{onClick:()=>T("Shopping",y),className:"ml-2 text-sm text-indigo-600",children:"Edit"}),i.jsx("button",{onClick:()=>m("Shopping",y),className:"ml-2 text-sm text-red-600",children:"Delete"})]},`s${N}`)),!(s.ancillaryServices&&s.ancillaryServices.length)&&!s.specialMeal&&!(s.shoppingRequests&&s.shoppingRequests.length)&&i.jsx("li",{children:"None"})]})]})]}),i.jsxs("div",{className:"mt-4",children:[i.jsx("h4",{className:"font-medium mb-2",children:"Add Service"}),S.length===0&&i.jsx("div",{className:"text-sm",children:"No additional services available for this passenger."}),S.map(y=>i.jsx("div",{className:"mb-3 p-3 border rounded bg-slate-50",children:i.jsxs("div",{className:"flex items-start gap-4",children:[i.jsxs("div",{className:"w-1/3",children:[i.jsx("strong",{className:"block",children:y}),i.jsxs("div",{className:"text-sm text-slate-600 mt-1",children:[y==="Meal"&&(s.specialMeal?`Availed: ${s.specialMeal}`:"Not availed"),y==="Ancillary"&&(s.ancillaryServices&&s.ancillaryServices.length?`Availed: ${s.ancillaryServices.join(", ")}`:"Not availed"),y==="Shopping"&&(s.shoppingRequests&&s.shoppingRequests.length?`Availed: ${s.shoppingRequests.join(", ")}`:"Not availed")]})]}),i.jsxs("div",{className:"w-2/3",children:[(()=>{const N=f&&f.serviceSubtypes&&f.serviceSubtypes[y]||[],z=y==="Meal"?"mealName":y==="Ancillary"?"ancillaryItem":"shoppingItems";return N.length>0?y==="Shopping"?i.jsx("div",{className:"mt-0",children:i.jsxs("label",{className:"block mb-2",children:[i.jsx("span",{className:"text-sm",children:"Choose shopping items"}),i.jsx("select",{multiple:!0,value:h.shoppingItems||[],onChange:C=>{const G=Array.from(C.target.selectedOptions).map(M=>M.value);p({...h,shoppingItems:G})},className:"px-3 py-2 border rounded w-full",style:{maxHeight:"160px"},children:N.map(C=>i.jsx("option",{value:C,children:C},C))})]})}):i.jsx("div",{className:"mt-0",children:i.jsxs("label",{className:"block mb-2",children:[i.jsxs("span",{className:"text-sm",children:["Choose ",y," subtype"]}),i.jsxs("select",{value:h[z]||"",onChange:C=>p({...h,[z]:C.target.value}),className:"px-3 py-2 border rounded w-full",children:[i.jsx("option",{value:"",children:"-- select --"}),N.map(C=>i.jsx("option",{value:C,children:C},C))]})]})}):y==="Meal"?i.jsx("div",{className:"mt-0",children:i.jsxs("label",{className:"block mb-2",children:[i.jsx("span",{className:"text-sm",children:"Meal Name"}),i.jsx("input",{type:"text",value:h.mealName||"",onChange:C=>p({...h,mealName:C.target.value}),className:"px-3 py-2 border rounded w-full"})]})}):y==="Ancillary"?i.jsx("div",{className:"mt-0",children:i.jsxs("label",{className:"block mb-2",children:[i.jsx("span",{className:"text-sm",children:"Ancillary Item"}),i.jsx("input",{type:"text",value:h.extraBaggage||"",onChange:C=>p({...h,extraBaggage:C.target.value}),className:"px-3 py-2 border rounded w-full"})]})}):i.jsx("div",{className:"mt-0",children:i.jsxs("label",{className:"block mb-2",children:[i.jsx("span",{className:"text-sm",children:"Shopping Items (comma-separated)"}),i.jsx("input",{type:"text",value:(h.shoppingItems||"").toString(),onChange:C=>p({...h,shoppingItems:C.target.value}),className:"px-3 py-2 border rounded w-full"})]})})})(),i.jsx("div",{className:"mt-2 flex justify-end",children:i.jsxs("button",{onClick:()=>g(y),className:"px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700",children:["Add ",y]})})]})]})},y))]})]})}function kp(){const{id:s}=Vl(),{state:f}=At(),d=$e(),[r,h]=O.useState(f?.passenger||null);O.useEffect(()=>{let g=!0;async function m(){if(f?.passenger){h(f.passenger);return}try{const T=await jp(Number(s));if(!g)return;h(T)}catch(T){console.error("Failed to load passenger",T),g&&h(null)}}return m(),()=>{g=!1}},[s,f]);const p=f?.flightId||r?.flightId,[S,_]=O.useState(f?.flight||null);return O.useEffect(()=>{let g=!0;async function m(){if(p)try{const T=await Jl(Number(p));if(!g)return;_(T)}catch(T){console.error("Failed to load flight",T)}}return m(),()=>{g=!1}},[p,f]),r?i.jsxs("div",{className:"min-h-screen p-6 bg-slate-50",children:[i.jsx("button",{onClick:()=>d(-1),className:"text-indigo-600 hover:underline mb-4",children:"← Back"}),i.jsx(Jp,{passenger:r,flight:S,onClose:()=>d(-1),onUpdate:async g=>{h(g);try{g.id&&await Hc(g.id,g)}catch(m){console.error("Failed to persist passenger update",m)}}})]}):i.jsx("div",{className:"p-6",children:"Passenger not found."})}function eh(){const s=$e(),[f,d]=O.useState(""),[r,h]=O.useState(""),[p,S]=O.useState(""),[_,g]=O.useState(""),m=(Sh||[]).filter(y=>{const N=y.name.toLowerCase().includes(f.toLowerCase()),z=r?y.date===r:!0,C=(p?y.route.startsWith(p):!0)&&(_?y.route.endsWith(_):!0);return N&&z&&C}),T=()=>{s(-1)};return i.jsxs("div",{className:"min-h-screen flex flex-col items-center bg-slate-50 p-6",children:[i.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[i.jsx("button",{onClick:T,className:"text-indigo-600 hover:text-indigo-800",children:"←"}),i.jsx("h1",{className:"text-2xl font-semibold",children:"Check-In Page"})]}),i.jsxs("div",{className:"flex flex-col gap-4 mb-6 w-full max-w-3xl",children:[i.jsx("input",{type:"text",value:f,onChange:y=>d(y.target.value),placeholder:"Search flights...",className:"px-4 py-2 border rounded"}),i.jsx("input",{type:"date",value:r,onChange:y=>h(y.target.value),className:"px-4 py-2 border rounded"}),i.jsxs("div",{className:"flex gap-4 items-center",children:[i.jsxs("label",{className:"flex-1",children:[i.jsx("span",{className:"block text-sm font-medium text-gray-700",children:"Start Location"}),i.jsx("input",{type:"text",value:p,onChange:y=>S(y.target.value),placeholder:"Start location",className:"w-full px-4 py-2 border rounded"})]}),i.jsxs("label",{className:"flex-1",children:[i.jsx("span",{className:"block text-sm font-medium text-gray-700",children:"Destination"}),i.jsx("input",{type:"text",value:_,onChange:y=>g(y.target.value),placeholder:"Destination",className:"w-full px-4 py-2 border rounded"})]})]})]}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-5xl",children:m.map(y=>i.jsxs("div",{className:"p-4 border rounded shadow bg-white cursor-pointer",onClick:()=>s(`/staff/check-in/${y.id}`),children:[i.jsx("h2",{className:"text-lg font-semibold mb-2",children:y.name}),i.jsxs("p",{className:"text-sm text-gray-600",children:["Date: ",y.date]}),i.jsxs("p",{className:"text-sm text-gray-600",children:["Route: ",y.route]}),i.jsxs("p",{className:"text-sm text-gray-600",children:["Departure: ",y.departureTime]}),i.jsxs("p",{className:"text-sm text-gray-600",children:["Arrival: ",y.arrivalTime]})]},y.id))})]})}let Sh=[];(async()=>{try{Sh=await Kl()}catch(s){console.error("Failed to load flights",s)}})();function th(){const{flightId:s}=Vl(),f=$e(),[d,r]=O.useState(null),[h,p]=O.useState(""),[S,_]=O.useState("All"),[g,m]=O.useState([]),[T,y]=O.useState([]);O.useEffect(()=>{let M=!0;async function L(){try{const K=await Jl(Number(s));if(!M)return;r(K),y(K?.seatMap||[]);const Q=await qc(Number(s));if(!M)return;m(Q)}catch(K){console.error("Failed to load flight or passengers",K)}}return L(),()=>{M=!1}},[s]);const N=M=>{p(M.target.value)},z=g.filter(M=>{const L=(M.name||"").toLowerCase().includes(h.toLowerCase()),K=S==="Not Checked-In"?!M.checkedIn:S==="Wheelchair Required"?!!M.wheelchair:S==="Infant"?!!M.infant:!0;return L&&K}),C=(M,L)=>{const K=g.map(W=>W.id===M?{...W,seat:L?String(L):""}:W);m(K);const Q=new Set;K.forEach(W=>{const w=parseInt((W.seat||"").toString().match(/^\d+/)?.[0],10);isNaN(w)||Q.add(w)}),y(W=>W.map(w=>({...w,isBooked:Q.has(w.number)})))},G=M=>{const L=g.map(K=>K.id===M?{...K,checkedIn:!0}:K);m(L)};return i.jsxs("div",{className:"p-6",children:[d?i.jsxs(i.Fragment,{children:[i.jsx("h1",{className:"text-2xl font-semibold mb-4",children:"Flight Details"}),i.jsxs("p",{children:[i.jsx("strong",{children:"Flight ID:"})," ",d.id]}),i.jsxs("p",{children:[i.jsx("strong",{children:"Aircraft Type:"})," ",d.aircraftType]}),i.jsxs("p",{children:[i.jsx("strong",{children:"Seats:"})," ",d.totalSeats," (Available: ",d.availableSeats,")"]})]}):i.jsx("div",{className:"p-6",children:"Loading flight information..."}),i.jsxs("div",{className:"mt-6",children:[i.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Services"}),i.jsx("div",{className:"flex gap-4",children:d.services.map(M=>i.jsx("div",{className:"p-4 border rounded shadow bg-white",children:M},M))})]}),i.jsxs("div",{className:"mt-6",children:[i.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Passengers"}),i.jsxs("div",{className:"flex gap-4 mb-4",children:[i.jsx("input",{type:"text",placeholder:"Search passengers...",value:h,onChange:N,className:"px-4 py-2 border rounded"}),i.jsxs("label",{className:"w-48",children:[i.jsx("span",{className:"block text-sm font-medium text-gray-700",children:"Show"}),i.jsxs("select",{value:S,onChange:M=>_(M.target.value),className:"w-full px-3 py-2 border rounded",children:[i.jsx("option",{children:"All"}),i.jsx("option",{children:"Not Checked-In"}),i.jsx("option",{children:"Wheelchair Required"}),i.jsx("option",{children:"Infant"})]})]})]}),i.jsxs("table",{className:"w-full border-collapse border border-gray-300",children:[i.jsx("thead",{children:i.jsxs("tr",{children:[i.jsx("th",{className:"border p-2",children:"Name"}),i.jsx("th",{className:"border p-2",children:"Services Availed"}),i.jsx("th",{className:"border p-2",children:"Seat"})]})}),i.jsx("tbody",{children:z.map(M=>i.jsxs("tr",{children:[i.jsx("td",{className:"border p-2",children:i.jsx("span",{className:"text-indigo-600 cursor-pointer hover:underline",onClick:()=>f(`/staff/check-in/${d.id}/${M.name}`),children:M.name})}),i.jsx("td",{className:"border p-2",children:(M.services||[]).join(", ")}),i.jsx("td",{className:"border p-2",children:M.checkedIn?i.jsxs("select",{value:M.seat||"",onChange:L=>C(M.id,L.target.value?parseInt(L.target.value,10):""),className:"w-full px-2 py-1 border rounded",children:[i.jsx("option",{value:"",children:"Unassigned"}),T.filter(L=>!L.isBooked||M.seat&&parseInt((M.seat||"").toString().match(/^\d+/)?.[0],10)===L.number).map(L=>i.jsx("option",{value:L.number,children:L.number},L.number))]}):i.jsxs("div",{className:"flex items-center gap-2",children:[i.jsx("span",{className:"text-sm text-gray-500",children:"Unassigned"}),i.jsx("button",{onClick:()=>G(M.id),className:"px-3 py-1 bg-green-600 text-white rounded text-sm",children:"Check in"})]})})]},M.id))})]})]}),i.jsxs("div",{className:"mt-6",children:[i.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Seat Map"}),(()=>{const M=T||[],L=new Map;return g.forEach(K=>{const Q=parseInt((K.seat||"").toString().match(/^\d+/)?.[0],10);isNaN(Q)||(L.has(Q)||L.set(Q,[]),L.get(Q).push(K))}),i.jsxs(i.Fragment,{children:[i.jsx("div",{className:"grid grid-cols-6 gap-2",children:M.map(K=>{const Q=L.get(K.number)||[];let W="";return Q.length>0?W="bg-red-400 text-white":K.isBooked?W="bg-gray-200":W="bg-white",i.jsxs("div",{className:`border border-gray-300 rounded p-3 text-center text-sm ${W}`,children:[i.jsx("div",{className:"font-semibold",children:K.number}),i.jsx("div",{className:"mt-2 text-xs",children:Q.length>0?Q.map(w=>w.name).join(", "):K.isBooked?"Booked":"Free"})]},K.number)})}),i.jsxs("div",{className:"mt-3 flex gap-3 items-center text-sm",children:[i.jsxs("div",{className:"flex items-center gap-2",children:[i.jsx("span",{className:"w-4 h-4 inline-block bg-red-400"})," Occupied"]}),i.jsxs("div",{className:"flex items-center gap-2",children:[i.jsx("span",{className:"w-4 h-4 inline-block bg-white border"})," Free"]}),i.jsxs("div",{className:"flex items-center gap-2",children:[i.jsx("span",{className:"w-4 h-4 inline-block bg-gray-200"})," Booked (no data)"]})]})]})})()]})]})}function lh(){const{flightId:s,username:f}=Vl(),[d,r]=O.useState(null),[h,p]=O.useState(null),[S,_]=O.useState([]),[g,m]=O.useState([]),[T,y]=O.useState({});O.useEffect(()=>{let C=!0;async function G(){try{const M=await yh(f),L=M&&M.length>0?M[0]:null;if(!C)return;if(r(L),L&&L.flightId){const K=await Jl(L.flightId);if(!C)return;p(K);const Q=(L?.services||[]).map(W=>typeof W=="string"?{type:W}:W);_(Q),m((K?.services||[]).filter(W=>!Q.some(w=>w.type===W)))}}catch(M){console.error("Failed to load passenger/flight",M)}}return G(),()=>{C=!1}},[s,f]);const N={Meal:15,Ancillary:25,Shopping:50},z=C=>{if(C==="Meal"){const G={type:"Meal",mealType:T.mealType||"N/A",mealName:T.mealName||"N/A",price:N.Meal};_(M=>[...M,G])}else if(C==="Ancillary"){const G={type:"Ancillary",extraBaggage:T.extraBaggage||0,price:N.Ancillary};_(M=>[...M,G])}else if(C==="Shopping"){const G={type:"Shopping",shoppingItems:T.shoppingItems||[],price:N.Shopping};_(M=>[...M,G])}m(G=>G.filter(M=>M!==C)),y({})};return d?i.jsxs("div",{className:"p-6",children:[i.jsx("h1",{className:"text-2xl font-semibold mb-4",children:"Passenger Details"}),i.jsxs("p",{children:[i.jsx("strong",{children:"Name:"})," ",d.name]}),i.jsxs("p",{children:[i.jsx("strong",{children:"Phone Number:"})," ",d.phoneNumber||"N/A"]}),i.jsxs("p",{children:[i.jsx("strong",{children:"Address:"})," ",d.address||"N/A"]}),i.jsxs("p",{children:[i.jsx("strong",{children:"Traveling From:"})," ",d.from||"N/A"]}),i.jsxs("p",{children:[i.jsx("strong",{children:"Traveling To:"})," ",d.to||"N/A"]}),i.jsxs("p",{children:[i.jsx("strong",{children:"Seat Assigned:"})," ",d.seat]}),i.jsxs("div",{className:"mt-6",children:[i.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Services Availed"}),i.jsx("ul",{className:"list-disc pl-6",children:S.map((C,G)=>i.jsxs("li",{className:"mb-2",children:[i.jsx("strong",{children:C.type||C}),C.type==="Meal"&&i.jsxs("ul",{className:"list-disc pl-6",children:[i.jsxs("li",{children:[i.jsx("strong",{children:"Type:"})," ",C.mealType]}),i.jsxs("li",{children:[i.jsx("strong",{children:"Meal Name:"})," ",C.mealName]}),i.jsxs("li",{children:[i.jsx("strong",{children:"Price:"})," $",C.price]})]}),C.type==="Ancillary"&&i.jsxs("ul",{className:"list-disc pl-6",children:[i.jsxs("li",{children:[i.jsx("strong",{children:"Extra Baggage:"})," ",C.extraBaggage," kg"]}),i.jsxs("li",{children:[i.jsx("strong",{children:"Price:"})," $",C.price]})]}),C.type==="Shopping"&&i.jsxs("ul",{className:"list-disc pl-6",children:[i.jsxs("li",{children:[i.jsx("strong",{children:"Items:"})," ",C.shoppingItems.length>0?C.shoppingItems.join(", "):"None"]}),i.jsxs("li",{children:[i.jsx("strong",{children:"Price:"})," $",C.price]})]})]},G))})]}),i.jsxs("div",{className:"mt-6",children:[i.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Add Service"}),i.jsx("ul",{className:"list-disc pl-6",children:g.map(C=>i.jsxs("li",{className:"mb-4",children:[i.jsx("strong",{children:C}),C==="Meal"&&i.jsxs("div",{className:"mt-2",children:[i.jsxs("label",{className:"block mb-2",children:[i.jsx("span",{className:"text-sm",children:"Meal Type"}),i.jsxs("select",{value:T.mealType||"",onChange:G=>y({...T,mealType:G.target.value}),className:"px-4 py-2 border rounded w-full",children:[i.jsx("option",{value:"",children:"Select"}),i.jsx("option",{value:"Veg",children:"Veg"}),i.jsx("option",{value:"Non-Veg",children:"Non-Veg"})]})]}),i.jsxs("label",{className:"block mb-2",children:[i.jsx("span",{className:"text-sm",children:"Meal Name"}),i.jsx("input",{type:"text",value:T.mealName||"",onChange:G=>y({...T,mealName:G.target.value}),className:"px-4 py-2 border rounded w-full"})]})]}),C==="Ancillary"&&i.jsx("div",{className:"mt-2",children:i.jsxs("label",{className:"block mb-2",children:[i.jsx("span",{className:"text-sm",children:"Extra Baggage (kg)"}),i.jsx("input",{type:"number",value:T.extraBaggage||"",onChange:G=>y({...T,extraBaggage:G.target.value}),className:"px-4 py-2 border rounded w-full"})]})}),C==="Shopping"&&i.jsx("div",{className:"mt-2",children:i.jsxs("label",{className:"block mb-2",children:[i.jsx("span",{className:"text-sm",children:"Shopping Items (comma-separated)"}),i.jsx("input",{type:"text",value:T.shoppingItems||"",onChange:G=>y({...T,shoppingItems:G.target.value.split(",")}),className:"px-4 py-2 border rounded w-full"})]})}),i.jsxs("button",{onClick:()=>z(C),className:"mt-2 px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700",children:["Add ",C]})]},C))})]})]}):i.jsx("div",{className:"p-6",children:"Passenger not found."})}function $p(){return i.jsxs("div",{className:"min-h-screen bg-slate-50",children:[i.jsx("nav",{className:"p-4 bg-white shadow",children:i.jsx("div",{className:"container mx-auto flex gap-4",children:i.jsx(Ll,{to:"/",className:"text-indigo-600 font-medium",children:"Login"})})}),i.jsx("main",{className:"container mx-auto p-6",children:i.jsxs(Fv,{children:[i.jsx(Se,{path:"/",element:i.jsx(dp,{})}),i.jsxs(Se,{path:"/admin",element:i.jsx(mp,{}),children:[i.jsx(Se,{index:!0,element:i.jsx("div",{className:"p-4 bg-white rounded",children:"Select an admin area from above."})}),i.jsx(Se,{path:"passengers",element:i.jsx(Wd,{})}),i.jsx(Se,{path:"passengers/:name",element:i.jsx(zp,{})}),i.jsx(Se,{path:"passengers/:flightId/passengerlist",element:i.jsx(Wd,{})}),i.jsx(Se,{path:"flights",element:i.jsx(Tp,{})}),i.jsx(Se,{path:"flights/new",element:i.jsx(Pd,{})}),i.jsx(Se,{path:"flights/:id",element:i.jsx(Pd,{})}),i.jsx(Se,{path:"routes",element:i.jsx(Ap,{})}),i.jsx(Se,{path:"users",element:i.jsx(Gp,{})}),i.jsx(Se,{path:"users/new",element:i.jsx(Id,{})}),i.jsx(Se,{path:"users/:username",element:i.jsx(Id,{})}),i.jsx(Se,{path:"travel-history",element:i.jsx(Yp,{})})]}),i.jsx(Se,{path:"/staff",element:i.jsx(Lp,{})}),i.jsx(Se,{path:"/passenger",element:i.jsx(Xp,{})}),i.jsx(Se,{path:"/passenger/book",element:i.jsx(Zp,{})}),i.jsx(Se,{path:"/passenger/confirm",element:i.jsx(Vp,{})}),i.jsx(Se,{path:"/staff/check-in",element:i.jsx(eh,{})}),i.jsx(Se,{path:"/staff/check-in/:flightId",element:i.jsx(th,{})}),i.jsx(Se,{path:"/staff/check-in/:flightId/:username",element:i.jsx(lh,{})}),i.jsx(Se,{path:"/inflight-service",element:i.jsx(Kp,{})}),i.jsx(Se,{path:"/inflight-service/passenger/:id",element:i.jsx(kp,{})}),i.jsx(Se,{path:"/checkin",element:i.jsx(eh,{})}),i.jsx(Se,{path:"/checkin/:flightId",element:i.jsx(th,{})}),i.jsx(Se,{path:"/checkin/:flightId/:username",element:i.jsx(lh,{})})]})})]})}lv.createRoot(document.getElementById("root")).render(i.jsx(O.StrictMode,{children:i.jsx(ap,{children:i.jsx($p,{})})}));
